import { Catch } from '@midwayjs/decorator'
import { httpError, MidwayHttpError } from '@midwayjs/core'
import { Context } from '@midwayjs/web'

@Catch(httpError.NotFoundError)
export class NotFoundFilter {
  async catch(err: MidwayHttpError, ctx: Context) {
    const env = ctx.app.config.env
    if (env === 'production' || env === 'prod') {
      // 404 错误会到这里
      // ctx.redirect('/404')

      ctx.status = 404
      return await ctx.service.staticPage.renderStaticPage(ctx.status.toString())
    }

    throw err
  }
}
