import { Catch } from '@midwayjs/decorator'
import { Context } from '@midwayjs/web'

@Catch()
export class DefaultErrorFilter {
  async catch(err: Error, ctx: Context) {
    const env = ctx.app.config.env
    if (env === 'production' || env === 'prod') {
      // 500 错误会到这里
      // ctx.redirect('/500')
      await ctx.service.staticPage.renderStaticPage(ctx.status.toString())
      return
    }
    throw err
  }
}
