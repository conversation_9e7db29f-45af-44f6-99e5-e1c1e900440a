import { URL } from 'url'
import { Application } from '@midwayjs/web'
import cookie from 'cookie'
import proxy from 'koa-proxies'
import logger from '../share/logger'
import { match } from '../share/api.config'

export default (app: Application) => {
  const { API_URL } = app.config.envConfig

  const isDev = ['local', 'development'].includes(app.config.env)
  const middleware = app.getMiddleware()
  /**
   * Guest Checkout API 代理
   *
   * ### Cookie 混淆算法：
   * 加密：1. 头七位移到末尾, 2. base64 编码
   * 解密：1. base64 解码, 2. 末七位移到开头
   *
   * 例如：
   * 原始输入：JadPbg-SWLXj6daQVlzMR422og3zx6lBvWUdcX3xYWo_
   * 移位：SWLXj6daQVlzMR422og3zx6lBvWUdcX3xYWo_JadPbg-
   * 编码输出：U1dMWGo2ZGFRVmx6TVI0MjJvZzN6eDZsQnZXVWRjWDN4WVdvX0phZFBiZy0=
   *
   * ### URL 代理规则：
   * 命中 /xos_gc/* 开头的 path 就走代理
   *
   * 例如：
   * '/xos_gc/v3/example' -> 'http://www.klook.com/v3/example'
   */
  middleware.insertBefore(
    proxy('/xos_gc', (_, ctx) => {
      const target = match(ctx.path) || API_URL
      return {
        target,
        logs: true,
        changeOrigin: true,
        rewrite: (path: string) => path.replace(/^\/xos_gc\//, '/'),
        events: {
          proxyReq(proxyReq: any, req: any) {
            const proxyUrl = new URL(req.url, target).toString()
            logger.info(
              '-> PROXY',
              req.requestId,
              req.method,
              req.oldPath,
              '=>',
              proxyUrl
            )
            const cookieObj = cookie.parse(req.headers.cookie || '')
            // 从 cookie 中读取 _pt_gc，解码、移位，然后赋值到 _pt
            if (typeof cookieObj._pt_gc === 'string') {
              const token = Buffer.from(cookieObj._pt_gc, 'base64').toString(
                'ascii'
              )

              cookieObj._pt = token.slice(-7) + token.slice(0, -7)

              // 透传修改后的 cookie 给上游服务
              proxyReq.setHeader(
                'cookie',
                Object.keys(cookieObj)
                  .map(key => cookie.serialize(key, cookieObj[key]))
                  .join('; ')
              )
            }
          }
        }
      }
    }),
    'bodyParser'
  )

  if (isDev) {
    // https://github.com/vagusX/koa-proxies/issues/55
    middleware.insertBefore(
      proxy('/v3/userserv/user/captcha_service/captcha_init', {
        target: API_URL,
        logs: true,
        changeOrigin: true
      }),
      'bodyParser'
    )

    /**
     * 服务端反向代理
     * @Description 联调产线，预发布等没有跨域处理的环境
     * @step 将local.config.ts的API_URL_BROWSER设置为本地地址即可如'http://127.0.0.1:3001'
     */

    middleware.insertBefore(
      proxy('/:path(v\\d+|rest|query)/(.*)', (_, ctx) => ({
        target: API_URL,
        changeOrigin: true,
        events: {
          proxyReq(_, req: any) {
            const target = match(ctx.path) || API_URL
            const proxyUrl = new URL(req.url, target).toString()
            logger.info(
              '-> PROXY',
              ctx.keplerId,
              ctx.url,
              req.method,
              req.oldPath,
              '=>',
              proxyUrl
            )
          }
        }
      })),
      'bodyParser'
    )
  }
}
