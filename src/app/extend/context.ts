import { Context } from '@midwayjs/web'
import EggCookies from 'egg-cookies'
import { createAxios } from '../../share/axios-guest'
// import { Context } from 'egg'
const GuestAxios = Symbol('Context#guestAxios')

const cookieOpt = { signed: false, httpOnly: false }

const contextExtend = {
  // is CSR
  get useCsrRender(): boolean {
    const ctx = this as unknown as Context

    const csrCheckRules = [
      // 1.降级插件 guardian csrCtxFlagKey
      ctx => ctx.guardianCsrFlag,

      // query _csr
      ctx => ctx.query._csr
    ]
    return csrCheckRules.some(rule => rule(ctx))
  },

  clientRuntimeState: {},
  set_cookies(this: Context, ...args: Parameters<EggCookies['set']>) {
    return this.cookies.set(args[0], args[1], {
      ...cookieOpt,
      ...args[2]
    })
  },

  get_cookies(this: Context, ...args: Parameters<EggCookies['get']>) {
    return this.cookies.get(args[0], {
      ...cookieOpt,
      ...args[1]
    })
  },

  injectHeadMeta(tag: string, head = false) {
    const ctx = this as any
    const ssrContext = ctx.ssrContext || {}
    const headFirst = (ssrContext.headFirst || '')
    ssrContext.headFirst = head ? (tag + headFirst) : headFirst + tag
    ctx.ssrContext = ssrContext
  },

  get guestAxios(): ReturnType<typeof createAxios> {
    const ctx = this as unknown as Context
    if (!this[GuestAxios]) {
      this[GuestAxios] = createAxios(ctx)
    }
    return this[GuestAxios]
  },

  setClientState(payload: Record<string, any>) {
    this.clientRuntimeState = {
      ...this.clientRuntimeState,
      ...payload
    }
  },

  get clientConf() {
    const ctx = this as unknown as Context
    return {
      kvClientConfig: {
        ...(ctx.app as any).kvClientConfig
      },
      ...Object.keys(ctx.clientRuntimeState).length ? {
        clientRuntimeState: {
          ...ctx.clientRuntimeState
        }
      } : {}
    }
  },

  get clientConfString() {
    return JSON.stringify(this.clientConf)
  }
}

export default contextExtend
