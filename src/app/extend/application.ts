
const KVClientConfig = Symbol('Application#kvClientConfig')

const appExtend = {
  currentTime: Date.now(),
  get kvClientConfig(): Record<string, any> {
    const duration = Date.now() - this.currentTime
    if (!this[KVClientConfig] || duration > 60000) {
      this[KVClientConfig] = (process as any)._kvClientEnv || {}
      this.currentTime = Date.now()
    }
    return this[KVClientConfig]
  }
}

export default appExtend
