import { Get, Provide, Inject } from '@midwayjs/decorator'
import { Context } from 'egg'
import { IVueRenderService } from '../interface'
import { LangController } from '../decorator/lang-controller'
import { mainRoute } from '../util/route'
import { createCsrFallbackNotify } from '../share/lark'
import { usePartResidenceCallback, useResidenceCallBack } from '../share/customCallback'
import kvConfig from '../config/klook.kv.config'

const { CacheHtml } = require('@klook/klook-node-framework-cache/lib/cache')
const csrRouterMiddleware = require('@klook/egg-guardian/lib/router-middleware/csr-router-middleware')

const csrNotify = createCsrFallbackNotify('首页CSR降级(SSR Render)', 1)

@Provide()
@LangController()
export class Index {
  @Inject()
  declare ctx: Context

  @Inject('VueRenderService')
  declare vueRenderService: IVueRenderService

  // main page
  @Get(mainRoute)
  async renderAll() {
    await this.vueRenderService.render()
  }

  @Get('/', {
    middleware: [
      CacheHtml({
        enable: true,
        serverSideTesting: ['reco_home_destination', 'promo_path'],
        callBack: (ctx) => {
          const trafficChannelLevelOne = ctx.trafficState?.trafficCommon?.channel_level_1
          const experimentList = ctx.experimentsHitList || (ctx.experiment && ctx.experiment.experimentsHitList)

          // https://klook.sg.larksuite.com/docx/Nscmdf6d2oOqU9xeOcClcdCIgob
          if (trafficChannelLevelOne === 'SEM' && experimentList?.hide_sem_coupon?.group?.id === 22688) {
            return { enable: false }
          }
          return usePartResidenceCallback(ctx, !!kvConfig.guardian?.homePageCacheUseWhiteList)
        }
      }),
      ...(kvConfig.guardian?.homePageSsrRenderQps ? [csrRouterMiddleware({
        qps: kvConfig.guardian?.homePageSsrRenderQps,
        printLog(_ctx, isCsr, curQps, options) {
          csrNotify(isCsr, curQps, options.qps)
          // 超过需要告警
          // console.log(`> ${ctx.path} ${isCsr} curQps: ${curQps} options: ${options.qps}`)
        }
      })
      ] : [])
    ]
  })
  async renderHome() {
    await this.vueRenderService.render()
  }

  // @Get('/test-home/', {
  //   middleware: [
  //     async function noIndex(ctx, next) {
  //       ctx.set('X-Robots-Tag', 'noindex')
  //       await next()
  //     },
  //     CacheHtml({
  //       enable: true,
  //       serverSideTesting: ['reco_home_destination', 'promo_path'],
  //       callBack: ctx => usePartResidenceCallback(ctx, !!kvConfig.guardian?.homePageCacheUseWhiteList)
  //     }),
  //     ...(kvConfig.guardian?.homePageSsrRenderQps ? [csrRouterMiddleware({
  //       qps: kvConfig.guardian?.homePageSsrRenderQps,
  //       printLog(_ctx, isCsr, curQps, options) {
  //         csrNotify(isCsr, curQps, options.qps)
  //         // 超过需要告警
  //         // console.log(`> ${ctx.path} ${isCsr} curQps: ${curQps} options: ${options.qps}`)
  //       }
  //     })
  //     ] : [])
  //   ]
  // })
  // async renderTestHome() {
  //   await this.vueRenderService.render()
  // }

  @Get('/careers/')
  async renderCareers() {
    if (this.ctx.language.language === 'ja') {
      this.ctx.status = 301
      this.ctx.redirect('https://www.klookcareers.com/tokyo')
    } else {
      await this.vueRenderService.render()
    }
  }

  @Get('/deals/', {
    middleware: [
      CacheHtml({
        enable: true,
        callBack: useResidenceCallBack
      })
    ]
  })
  async renderDeals() {
    await this.vueRenderService.render()
  }

  @Get('/travel-deals/')
  async renderTravelDeals() {
    const { path, querystring } = this.ctx
    this.ctx.status = 301
    const redirectUrl: string = querystring ? `${path.replace('travel-deals', 'deals')}?${querystring}` : `${path.replace('travel-deals', 'deals')}`
    await this.ctx.redirect(redirectUrl)
  }

  @Get('/travel-deals/(coureg|city)/*')
  async renderTravelDealsCity() {
    const { path, querystring } = this.ctx
    this.ctx.status = 301
    const redirectUrl: string = querystring ? `${path.replace('travel-deals', 'deals')}?${querystring}` : `${path.replace('travel-deals', 'deals')}`
    await this.ctx.redirect(redirectUrl)
  }

  @Get('/city/*', {
    middleware: [
      CacheHtml({
        enable: true,
        serverSideTesting: ['1070000-web-billboard', 'need_recommend_navigation_node_v3', 'city_node_deeplink', 'NewPOIModule_AB_Test', 'NewCityPopularActiviy_AB_Test'],
        callBack: usePartResidenceCallback
      })
    ]
  })
  async renderCity() {
    await this.vueRenderService.render()
  }

  @Get('/poi/*', {
    middleware: [
      CacheHtml({
        enable: true
      })
    ]
  })
  async renderPoi() {
    await this.vueRenderService.render()
  }

  @Get('/destination/*', {
    middleware: [
      CacheHtml({
        enable: true
      })
    ]
  })
  async renderDestination() {
    await this.vueRenderService.render()
  }

  @Get('/value-pack/*', {
    middleware: [
      CacheHtml({
        enable: true
      })
    ]
  })
  async renderValuePack() {
    await this.vueRenderService.render()
  }

  @Get('/joypersonalityquiz/*', {
    middleware: [
      CacheHtml({
        enable: true
      })
    ]
  })
  async renderJoypersonalityquiz() {
    await this.vueRenderService.render()
  }

  @Get('/tw_invoice/')
  async renderTWInvoice() {
    if (this.ctx.language.language !== 'zh-TW') {
      this.ctx.status = 404
    } else {
      await this.vueRenderService.render()
    }
  }

  @Get('/tw_receipt/')
  async renderTWReceipt() {
    if (this.ctx.language.language !== 'zh-TW') {
      this.ctx.status = 404
    } else {
      await this.vueRenderService.render()
    }
  }

  // 404 page
  @Get('/404/')
  async render404() {
    await this.ctx.service.staticPage.renderStaticPage('404')
    this.ctx.status = 404
  }

  // 404 page
  @Get('/500/')
  async render500() {
    await this.ctx.service.staticPage.renderStaticPage('500')
    this.ctx.status = 500
  }

  // ping
  @Get('/ping/')
  healthCheck() {
    this.ctx.body = 'ok'
  }

  @Get('/actuator/prometheus/')
  prometheus() {
    this.ctx.body = 'ok'
  }

  @Get('/search/')
  async renderSearch() {
    const querystring: string = this.ctx.querystring || ''
    if (this.ctx.deviceInfo.platform === 'desktop' || /(query|template_id|tag_id|frontend_id_list|sort|price_from|price_to|instant|start_date|cardSize)+?/.test(querystring)) {
      let redirectUrl: string = `${this.ctx.path.replace('search', 'search/result')}?search_landing=true`
      if (querystring) {
        redirectUrl += `&${querystring.replace('frontend_id_list', 'front_id').replace('start_date', 'start_time')}`
      }
      return this.ctx.redirect(redirectUrl)
    }
    await this.vueRenderService.render()
  }
}
