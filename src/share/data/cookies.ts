type CookieName = 'language' | 'currency' | 'retina' | 'deviceId' | 'platform' | 'token' | 'preferSite' | 'residence'
interface CookieValue {
  key: string
  lifetime: number
}
type Cookies = Record<CookieName, CookieValue>
const cookies: Cookies = {
  language: {
    key: 'klk_lang',
    lifetime: 30 * 24 * 60 * 60 * 1000 // 30 days
  },
  currency: {
    key: 'klk_currency',
    lifetime: 7 * 24 * 60 * 60 * 1000 // 7 days
  },
  retina: {
    key: 'retina_support',
    lifetime: 30 * 24 * 60 * 60 * 1000 // 30 days
  },
  deviceId: {
    key: 'device_id_new',
    lifetime: 30 * 24 * 60 * 60 * 1000 // 30 days
  },
  platform: {
    key: 'klk_platform_status',
    lifetime: 0
  },
  token: {
    key: '_pt',
    lifetime: 7 * 24 * 60 * 60 * 1000 // 7 days
  },
  preferSite: {
    key: 'klk_ps',
    lifetime: 365 * 24 * 60 * 60 * 1000 // 365 days
  },
  residence: {
    key: 'klk_rdc', // residence
    lifetime: 24 * 60 * 60 * 1000 // 365 days
  }
}

export default cookies
