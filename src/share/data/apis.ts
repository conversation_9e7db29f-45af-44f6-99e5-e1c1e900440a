import { hotelPayPreBook, hotelSettlement, hotelPayBook } from './hotel-apis'

const apiNameMap = {
  verify: '/v3/userserv/user/profile_service/get_simple_profile_by_token', // GET 获取用户信息 - 简易版，用于校验 token
  profile: '/v3/userserv/user/profile_service/get_my_profile_by_token', // GET 获取用户信息
  simpleProfile: '/v3/userserv/user/profile_service/get_simple_profile_by_token', // GET 获取用户信息
  jsessionId: '/v1/usrcsrv/session/getkey', // GET jsessionid
  unreview: '/v1/usrcsrv/myreviews/unreview', // GET 获取用户未评价数
  updateBanner: '/v1/usrcsrv/component/campaign/banner', // GET 更新首页banner
  taiwanCoupon: '/v1/cashier/asset_voucher/klook_guolv/query', // GET 个人中心获取台湾国旅券
  menus: '/v1/usrcsrv/vertical/menu?source=3', // GET 分类菜单
  regions: '/v1/websrv/ranges', // GET 地区列表
  getOTA: '/v1/usrcsrv/ota/config?location=:location', // 获取OTA组件信息
  getSurvey: '/v1/webbffapi/public/survey/page/get?id=:id', // GET 问券信息
  hotDestinations: '/v1/usrcsrv/generic/types/102', // GET 热门目的地
  search: '/v1/usrcsrv/search/suggest', // GET 搜索
  search_suggest: '/v1/cardinfocenterservicesrv/search/platform/suggest', // GET 搜索建议
  changeLanguage: '/v3/userserv/user/profile_service/change_language', // POST 设置语言到个人信息里
  changeCurrency: '/v3/userserv/user/profile_service/change_currency', // POST 设置货币到个人信息里
  dealsPage: '/v1/usrcsrv/jenga/page/deals_page', // GET deals page页面信息
  promoPathPage: '/v2/usrcsrv/jenga/page/deals_page', // GET promo page 页面信息
  promoPathCountryList: '/v2/usrcsrv/deals/on/sale/country/list', // GET promo page country切换
  memberShipReviewList: '/v1/mpsrv/review/list', // GET mp评论列表信息
  memberShipOrderDetail: '/v1/mpsrv/jenga_page/order_detail', // GET memberShip orderDetail页面信息
  memberShip: '/v1/mpsrv/jenga/page/product_page', // GET memberShip page页面信息
  memberShip_v2: '/v2/mpsrv/jenga/page/product_page', // GET memberShip page页面信息
  memberShipBenefit: '/v1/mpsrv/benefit/info', // GET memberShip Benefit detail
  memberShipAddCart: '/v1/mpsrv/order/add/shoppingcart', // POST memberShip booknow
  memberOrderSettlement: '/v1/mpsrv/order/settlement', // POST order settlement
  memberOrderCouponList: '/v1/mpsrv/order/benefit/detail',
  memberOrderGenerate: '/v1/mpsrv/order/generate', // order generate
  memberRefundReasons: '/v1/mpsrv/get/refund/reasons', // GET membership获取退款理由接口
  memberRefundApply: '/v1/mpsrv/order/refund/apply', // POST membership申请退款接口
  memberRefundDetail: '/v1/mpsrv/order/refund/detail', // GET membership退款详情接口
  membershipInfo: '/v3/userserv/user/bff_service/simple_membership_get', // GET rewards membership详情接口
  membershipFeatureList: '/v1/mpsrv/feature/activity/list', // GET membership Feature Activitylist 列表接口
  wishListAdd: '/v3/usrcsrv/publish/wishlist/add',
  wishListCancel: '/v3/usrcsrv/publish/wishlist/cancel',

  billboardGroup: '/v1/usrcsrv/billboard/group', // 拉取榜单集合页
  billboardGroupDestinations: '/v1/usrcsrv/billboard/group/destinations', // 榜单集合页开放地区表单
  recommendBillboardList: '/v1/cardinfocenterservicesrv/public/recommend/rank/destination/rank_list', // 落地页榜单列表

  sendSmsCodeCaptcha: '/v3/userserv/user/profile_service/send_sms_code_with_captcha', // 带极验校验的验证码发送接口

  commonCoupon: '/v1/usrcsrv/refer/amount', // GET 通用的优惠券
  inviteCoupon: '/v1/usrcsrv/refer/friends', // GET 邀请的优惠券
  newInviteCoupon: '/v1/couponapisrv/refer/description', // 邀请的优惠券新接口
  recentlyView: '/v1/usrcsrv/home/<USER>/viewed', // GET 最近查看的活动和城市
  shoppingCartCount: '/v3/order/shoppingcart/count', // GET 购物车商品数量
  shoppingCartList: '/v3/order/shoppingcart', // GET 购物车商品列表
  conditions: '/v1/usrcsrv/generic/types/:type', // GET 使用条款、隐私策略、Cookie 策略、价格保障
  emailCreateSubscribe: '/v1/websrv/subscribe/:email/create', // POST 关联订阅邮件
  emailSendSubscribe: '/v1/websrv/subscribe/:email/send', // POST 订阅邮件
  logout: '/v3/usrcsrv/user/logout', // POST 退出登录
  downloadApk: '/web2/download.action', // GET 下载 APK
  destinationNav: '/v1/usrcsrv/destination/guide', // GET 获取目的地导航数据
  affiliateConf: '/v1/affinternalsrv/aid2uid', // aid配置接口

  faqDetail: '/v1/usrcsrv/faq/detail', // GET Faq 详情
  faqCategory: '/v1/usrcsrv/faq/category/get', // GET Category 分类
  faqSearchSuggest: '/v1/usrcsrv/faq/suggest', // GET 搜索建议
  categoryFaqs: '/v1/usrcsrv/faq/get/by/category', // GET Category 分类下的 Faq
  mostHelpfulFaqs: '/v1/usrcsrv/faq/most/helpful/get', // FAQ 首页重构，获取最有帮助的faq
  faqFeedback: '/v1/usrcsrv/faq/comment/add', // POST 反馈
  faqHelpful: '/v1/usrcsrv/faq/helpful/info/add', // POST点赞点踩

  experienceActivityDetail: '/v1/experiencesrv/activity/component_service/detail', // experiences 活动详情；楼层化接口
  experienceFastPassActivityDetail: '/v1/experiencesrv/dock/fast_pass_service/activity_detail', // experiences 扫码活动详情; 楼层化接口; query 参数：sales_channel=fast_pass&activity_id=524
  experienceActivityDetailForWhiteLabel: '/v1/experiencesrv/dock/white_label_service/activity_detail',
  activityDetail: '/v1/usrcsrv/activity/:activityId/detail', // GET 活动详情页活动数据
  activitySchedules: '/v1/usrcsrv/activity/:activityId/schedules', // GET 活动详情页日期
  activityReviewGallery: '/v1/usrcsrv/activities/:activityId/images/show', // GET 活动详情页reviews的图片的信息
  getActivityCards: '/v1/experiencesrv/activity/detail/cards', // GET 活动详情页活动卡片
  activityReviewGalleryImages: '/v1/usrcsrv/activities/:activityId/images/get', // GET 活动详情页reviews图片
  activityPackageDetail: '/v1/usrcsrv/packages/:packageId/base/published', // GET 活动详情页套餐详情
  activityPackageDetailPreview: '/v1/usrcsrv/packages/:packageId/base/all', // GET 活动页 preview 模式下的套餐详情
  activityReviews: '/v1/usrcsrv/activities/:activityId/reviews', // GET 活动详情页 reviews
  activityReviewsLikeAdd: '/v1/usrcsrv/review/like/add', // 活动详情页评论点击有帮助
  reviewFilter: '/v1/usrcsrv/review/filter/info', // GET 评论过滤
  activityCard: '/v1/usrcsrv/activities/:activityIdList/cards',
  activityTranslate: '/v2/usrcsrv/translate', // POST 活动页自动翻译
  activityPriceSchedules: '/v2/usrcsrv/activity/schedules', // GET 获取价格日历
  activityPromotions: '/v1/usrcsrv/activity/package_promo_price', // GET 获取活动的促销
  activityPackagePromotion: '/v1/usrcsrv/package/promo_price', // GET 获取套餐的促销
  activityPackagePriceList: '/v1/experiencesrv/packages/credits_and_promo_price', // POST 获取套餐卡片价格在某日期下的价格信息
  activityRedirectInfo: '/v1/experiencesrv/activity/detail_service/pre_info', // POST 活动页业务线分流
  activityTemplate: '/v1/prodbasesrv/activities/:activityId/basic', // POST 活动页业务线分流
  expressCheckOutRedirect: '/v1/experiencesrv/dock/fast_pass_service/express_check_out_redirect', // express_check_out入口页重定向

  // travel pass
  multiPassReservationPackages: '/v1/experiencesrv/travelpass/service/get_reservation_packages',
  getBookingDetailsRefundAbleSkus: '/v1/experiencesrv/order/refund_service/get_refund_able_skus',
  calcBookingDetailsRefundAmount: '/v1/experiencesrv/order/refund_service/calc_refund_amount',
  submitBookingDetailsRefund: '/v1/experiencesrv/order/refund_service/submit_refund',

  // manage booking
  experienceManageBookingInfos: '/v2/usrcsrv/order/alter/ticket/infos',
  experienceManageBookingRevert: '/v2/usrcsrv/order/alter/ticket/revert',
  experienceManageBookingSubmit: '/v2/usrcsrv/order/alter/ticket/submit',
  experienceManageBookingModify: '/v1/usrcsrv/packages/:packageId/schedules/modify',

  // transport ticket ptp
  ptpBasicInfo: '/v1/reappserv/ptp/basic_info', // GET 基础信息
  ptpSearchForm: '/v1/reappserv/ptp/:productType/search/form', // GET 获取搜索表单配置
  ptpSearchId: '/v1/reappserv/ptp/:productType/route/search', // POST 获取搜索 id
  ptpSearchResult: '/v3/reappserv/ptp/:productType/route/search/result', // POST 获取搜索结果及 sort
  ptpSearchFilter: '/v3/reappserv/ptp/:productType/route/search/filter', // GET  获取筛选条件
  ptpPolicy: '/v1/reappserv/ptp/:productType/route/ticket/policy', // GET 获取 ticket policy
  ptpFaq: '/v1/reappserv/rail/:productType/richcontent?action=getFaq', // GET 获取 faq
  ptpRouteDetail: '/v1/reappserv/ptp/:productType/route/detail', // GET 获取路线时刻详情
  ptpRouteSections: '/v3/reappserv/ptp/:productType/route/sections', // GET 获取路线详情接口骨架
  ptpPackageSelectApiName: 'route_detail_package_ticket', // 筛选路线详情接口骨架返回的接口，获取 package select 接口路径
  ptpTicketSelectApiName: 'route_detail_select_ticket', // 筛选路线详情接口骨架返回的接口，获取 ticket select 接口路径
  ptpPriceAdd: '/v3/reappserv/ptp/:productType/route/price/add', // POST 车票详情页计算价格
  ptpValidate: '/v2/reappserv/ptp/:productType/route/validate', // POST 价格和库存校验接口
  ptpAddonDetail: '/v1/reappserv/ptp/:productType/addon/detail', // GET addon 套餐详情
  ptpHomeSections: '/v2/reappserv/rail/homepage/sections', // 垂直页骨架接口
  ptpDlpDetail: '/v1/reappserv/ptp/:productType/dlp_page_detail', // dlp 页面数据接口
  ptpDlpMostRoute: '/v1/reappserv/ptp/:productType/dlp_most_route', // dlp 页面路线统计接口
  ptpStationSearch: '/v3/reappserv/ptp/:productType/location/search', // ptp站点搜索
  ptpNotice: '/v1/reappserv/rail/notice?product_type=:productType&withCredentials=false',
  ptpDlpSearchForm: '/v1/reappserv/ptp/:productType/search/dlp_form', // dlp search configs
  ptpDlpSearchResult: '/v1/reappserv/ptp/:productType/route/search/dlp_result', // dlp search result
  ptpCoupons: '/v1/reappserv/ptp/:productType/order/coupon',
  ptpCouponRedeem: '/v1/reappserv/ptp/:productType/order/redeem',
  ptpSettlementSections: '/v2/reappserv/ptp/:productType/checkout/sections',
  ptpSettlementSettle: '/v1/reappserv/ptp/:productType/order/settlement',
  ptpSettlementOrder: '/v1/reappserv/ptp/:productType/order/create',
  ptpSettlementStatus: '/v1/reappserv/ptp/order/poll/status',
  ptpSettlementGetSeats: '/v3/reappserv/ptp/:productType/seat/map',
  ptpSettlementGetPickupForm: '/v1/reappserv/ptp/:productType/pick_up_option/forms',
  ptpSettlementGetPassengerType: '/v2/reappserv/ptp/:productType/passenger/type',
  ptpSettlementGetAddonDetail: '/v1/reappserv/ptp/:productType/addon/detail',
  ptpSettlementGetAddonSchedule: '/v1/reappserv/ptp/:productType/addon/schedules',

  ptpWhiteLabelHomeSections: '/v1/reappserv/ptp/whitelabel/home/<USER>', // whiteLabel 垂直页骨架接口
  ptpWhiteLabelCheckoutSections: '/v1/reappserv/ptp/whitelabel/checkout/skeleton', // whiteLabel 结算页骨架接口
  ptpWhiteLabelSearchListSections: '/v1/reappserv/ptp/whitelabel/search/result/skeleton', // whiteLabel 搜索结果页配置接口
  ptpWhiteLabelRouteDetailSkeleton: '/v1/reappserv/ptp/whitelabel/route/detail/skeleton', // whitelabel 车票详情骨架接口
  ptpWhiteLabelOrderDetailSkeleton: '/v1/reappserv/ptp/whitelabel/order/detail/skeleton', // whitelabel 订单详情骨架接口

  /***
   * 更新心愿清单
   * @Method POST
   * @Params { cancel_activity_id: activityId } 取消收藏
   * @Params { add_activity_id: activityId } 添加到收藏
   */
  updateWishList: '/v1/usrcsrv/wishlist/update',
  addWishList: '/v1/usrcsrv/wishlist/add',
  cancelWishList: '/v1/usrcsrv/wishlist/cancel',
  getWishStatus: '/v1/usrcsrv/favorite_activity', // GET 获取活动是否加入到心愿清单
  getPackageSchedulesAndUnits: '/v1/usrcsrv/packages/:packageId/schedules_and_units', // GET 套餐的可选时间和unit
  getPackagePriceSchedulesAndUnits: '/v2/usrcsrv/packages/schedules_and_units', // GET 套餐价格日历
  getPackageUnits: '/v1/usrcsrv/arrangements/:arrangementId/units', // GET 获取套餐unit信息
  bookingNow: '/v3/order/booking/add', // POST 立即预订
  addToShoppingCard: '/v3/order/booking/edit', // POST 加入购物车
  getCategoryInfo: '/v3/userserv/user/profile_service/get_user_category_info', // GET 获取用户类别信息
  getPresalePackages: '/v1/usrcsrv/activity/rail/presale/info', // GET 获取预售套餐

  noticeList: '/v1/usrcsrv/notice', // GET 全局通知
  secondCateList: '/v1/usrcsrv/faq/root/category/get', // GET 获取二级分类
  carRentalsAdd: '/v1/transfercarrentalapisrv/carrental/order/noshoppingcart/get_cache_id', // 租车加入购物车
  carRentalOrderDetail: '/v1/transfercarrentalapisrv/carrental/order/detail',
  carRentalRefundReason: '/v1/transfercarrentalapisrv/carrental/order/refund/reason',
  carRentalGetAmount: '/v1/transfercarrentalapisrv/carrental/order/refund/get_amount',
  carRentalApplyAmount: '/v1/transfercarrentalapisrv/carrental/order/refund/apply_refund',
  carRentalRefundDetail: '/v1/transfercarrentalapisrv/carrental/order/refund/detail',
  orderUserCancel: '/v1/order/user/cancel',
  registerGuest: '/v3/usrcsrv/user/register/guest', // 注册guest用户

  // Kepler 系统相关
  getExperimentsHitList: '/v2/usrcsrv/hit/experiments',

  // gift card
  giftCardInformaton: '/v1/usrcsrv/egiftcard', // 购买电子礼品卡活动信息
  accountGiftCard: '/v1/usrcsrv/personal/giftcard', // 账户礼品卡信息
  redeemGiftCardCode: '/v1/order/trades/exchange', // 兑换礼品卡
  redeemCouponCode: '/v1/couponapisrv/redeem', // 兑换优惠券
  getCouponData: '/v1/usrcsrv/coupon/tag/act/entrance', // 获取优惠券信息
  getWalletsData: '/v1/order/wallets', // 礼品卡金额
  getWalletsTrades: '/v1/order/trades',

  preferredActivities: '/v1/usrcsrv/landing_page/preferred/activities', // klook-preferred 页面基础数据
  searchHit: '/v1/usrcsrv/search/hit', // 根据城市 filter 活动列表
  preferredRange: '/v1/usrcsrv/landing_page/preferred', // klook-preferred 页面的 filter 列表数据
  updateReopen: '/v1/usrcsrv/reopen/notice/update',
  activitySeoLink: 'v1/usrcsrv/activity/seo/content',

  homePage: '/v2/usrcsrv/component/page/home', // 首页
  travelPolicyPage: '/v1/usrcsrv/jenga/page/travel_policy_page', // 疫情查询页面
  mbtPage: '/v1/campaigngameserv/jenga/mbt/page', // 三单挑战活动页面
  slideWord: '/v1/cardinfocenterservicesrv/public/recommend/get_slide_words', // 轮播词
  loginWay: '/v3/userserv/user/profile_service/get_my_login_ways_by_token', // 获取用户账号绑定信息
  specialTermDetail: '/v3/userserv/user/term_service/get_special_term_detail', // 获取特殊协议
  uncheckSpecialTermDetail: '/v3/userserv/user/term_service/get_user_uncheck_special_term_info', // 获取特殊协议
  updateUncheckSpecialTermDetail: '/v3/userserv/user/term_service/update_user_special_term_status', // 获取特殊协议
  homeNearbyAll: '/v2/usrcsrv/nearby/cards/all', // GET 首页所有的nearby数据
  homePopularAll: '/v1/cardinfocenterservicesrv/public/recommend/home/<USER>/more_acts', // GET 首页所有的popular activity数据
  wifiFilter: '/v1/usrcsrv/wifi/filter_options', // GET 首页所有的popular activity数据

  siteChange: '/v1/usrcsrv/transformer/jump', // 切换主站分站
  changePreferSite: '/v3/userserv/user/profile_service/change_prefer_site', // 修改偏好站点
  // partnership 手机号领券相关
  getPhoneCouponInfo: '/v1/usrcsrv/generic/types/499', // 手机号领券在CMS配置的TYPE是499
  sendSmsCodeWithCaptcha: '/v3/userserv/user/register_service/send_sms_code_with_captcha',
  checkMobileAccount: '/v3/userserv/user/register_service/check_mobile_account',
  loginBySMS: '/v3/usrcsrv/user/login/mobile_sms',
  redeemCampaign: '/v1/redeemcodesrv/redeem/campaign/:campaignName',

  // 用户触达相关
  updateUserPrefer: '/v3/userserv/user/profile_service/update_user_notification_prefer', // POST 开启推送开关
  getPreferNotify: 'v1/usrcsrv/user/notification/prefer/get', // GET 获取用户订阅偏好

  // 检查账户是否存在（即将废弃）
  accountIsExist: '/v3/userserv/user/register_service/account_is_exist',
  accountIsExistThird: '/v3/userserv/user/register_service/account_is_exist_for_third_party',
  accountIsExistOnly: '/v3/userserv/user/register_service/account_is_exist_only',

  // google 登录
  googleLogin: '/v3/usrcsrv/user/login/google',
  googleBind: '/v3/userserv/user/profile_service/bind_google',

  // 条款相关
  getSpecialTerms: '/v3/userserv/user/term_service/get_special_term_detail',
  updateSpecialTermStatus: '/v3/userserv/user/term_service/update_user_special_term_status',

  // dot contact tracking
  SMSVefiry: '/v1/usrcsrv/dot/sms/verify', // GET 手机号验证
  getSMSCode: '/v1/usrcsrv/dot/sms/code', // GET 获取手机验证码
  submitDotEntrance: '/v1/usrcsrv/dot/entrance', // POST 入园信息提交
  submitDotExit: '/v1/usrcsrv/dot/visitor/exit', // POST 出园信息提交
  verifyPhone: '/v1/usrcsrv/dot/get/visitor', // POST 出园信息提交
  getAttraction: '/v1/usrcsrv/dot/get/attraction', // GET 查询园区信息接口

  getRecentlyPurchase: '/v1/usrcsrv/recently/purchased/act/cards', // GET 获取最近常买
  getBookingList: '/v1/usrcsrv/booking/list', // POST 订单列表
  getLogistics: '/v1/usrcsrv/logistics/detail', // GET 物流信息
  bookingPopularList: '/v1/cardinfocenterservicesrv/public/recommend/bookings/page/pop_acts', // GET 订单列表页popular activity数据
  deleteBooking: '/v1/usrcsrv/booking/sort/archive', // POST 删除
  cancelDeleteBooking: '/v1/usrcsrv/booking/cancel_archive', // POST 取消删除
  stickBooking: '/v1/usrcsrv/booking/sort/stick', // POST 置顶
  cancelStickBooking: '/v1/usrcsrv/booking/sort/cancel_stick', // POST 取消置顶
  comboQuery: '/v1/usrcsrv/booking/combo', // GET 联合支付/身份验证信息查询
  bookingManagement: '/v1/order/alter/ticket/infos', // POST 修改订单
  submitBookingManagement: '/v1/order/alter/ticket/submit', // POST 提交修改订单
  revertBookingManagement: '/v1/order/alter/ticket/revert', // POST 回退修改订单
  getHotKeyword: '/v1/usrcsrv/search/hot', // GET 搜索启动页接口
  getHotKeywordNew: '/v1/cardinfocenterservicesrv/search/getStartupPageInfo', // GET 新搜索启动页接口
  getHotKeywordDestination: '/v1/cardinfocenterservicesrv/search/getDestinationPageInfo', // GET 新搜索Destination启动页接口
  getSearchResult: '/v1/cardinfocenterservicesrv/search/platform/', // GET 搜索结果页接口，后加type complete_search | city_search | country_search

  getProgramList: '/v2/promosrv/program/apply/product/list', // 获取program适用活动列表页
  getprogramCoupon: '/v1/promosrv/program/manual_redeem', // POST 获取program 优惠券

  // experience
  getExperienceCategory: '/v1/experiencesrv/category/menu', // GET 首页L0+L1选择
  getExperienceDestination: '/v1/experiencesrv/destination/guide', // GET 首页 目的地
  getExperienceActivityNav: '/v1/experiencesrv/category/activity/menu', // GET 获取分类活动列表筛选项
  getExperienceHotCountry: '/v1/experiencesrv/hot/country', // GET 获取分类活动列表筛选项
  getExperiencePageInfo: '/v1/experiencesrv/page_info', // GET 获取分类活动列表筛选项
  getExperiencePreInfo: '/v1/experiencesrv/pre_info', // GET 获取分类活动列表筛选项
  getExperienceHotCity: '/v1/experiencesrv/hot/city', // GET 获取分类活动列表筛选项
  getExperienceMainBanner: '/v1/experiencesrv/banner/main', // GET 获取分类活动列表筛选项
  getExperienceReviewList: '/v1/experiencesrv/vertical/home_service/review',
  getExperienceNearbyActivity: '/v1/experiencesrv/nearby/activity', // GET 附近活动
  getExperienceNearbyCity: '/v1/experiencesrv/nearby/city', // GET 附近国家
  getExperienceBlog: '/v1/experiencesrv/blog/list', // GET 附近国家
  getExperienceNearbyMenu: '/v1/experiencesrv/category/menu', // GET 附近活动tab
  getExperienceActivityHighlight: '/v1/experiencesrv/activity/highlight', // GET 获得自动运营主题活动列表
  experienceSearch: '/v1/experiencesrv/search/suggest', // GET 获得自动运营主题活动列表
  experienceCitySeo: '/v1/usrcsrv/experience/city/seo/content', // GET 获得自动运营城市seo internal link
  experienceCountrySeo: '/v1/usrcsrv/experience/country/seo/content', // GET 获得自动运营国家seo internal link
  destinationSearch: '/v1/experiencesrv/search/suggest', // GET 目的地搜索
  getBreadcrumb: '/v1/experiencesrv/breadcrumb', // GET 面包屑
  getElevyTravelers: '/v1/usrcsrv/elevy/travelers',

  experiencesrvSearch: '/v1/experiencesrv/search/activity',
  getExperienceCategoryActivity: '/v1/experiencesrv/category/activity',
  sendEmail: '/v1/usrcsrv/user/feedback/email', // POST 发送邮件
  getOrderList: '/v1/order/myorders/list', // POST 获取订单列表
  getActivityDetail: '/v1/usrcsrv/activities/:id/cards', // GET 获取活动详情

  regenerateOrder: '/v1/bookingapiserv/generate/again', // 重新生成订单

  experienceMFOSections: '/v1/experiencesrv/component/page/mfo', // POST 获取订单列表
  experienceMFOActivityList: '/v1/experiencesrv/mfo/search/activity', // 活动列表

  getBookingDetailSections: '/v1/experiencesrv/order/detail_service/get_booking_detail', // GET 获取订单详情
  getBookingPaymentDetail: '/v1/experiencesrv/order/detail_service/payment_detail', // GET 获取支付明细
  postBookingChase: '/v1/order/booking/chase', // POST 催单
  getBookingRefundDetail: '/v1/experiencesrv/order/detail_service/refund_detail', // GET 退款详情

  // POI相关
  getPoiDetail: '/v1/usrcsrv/jenga/page/poi_page',
  getPoiReviewList: 'v1/usrcsrv/get/poi/review/list',
  getPoiActivityList: '/v1/cardinfocenterservicesrv/public/search/platform/poi_search',

  // Chat Entrance 相关
  getChatEntranceConfig: '/v1/faqchatcommsrv/unifiedentrance/config',
  // Chat&CEG 相关
  sendOfflineMsg: '/v1/faqchatcommsrv/comm/offline/message',
  chatAuth: '/v1/faqchatcommsrv/comm/zendesk/visitor/auth', // POST 初始化chat JWT 验证
  getOrderDetail: '/v1/usrcsrv/order/detail', // 用来检查是否是SRV活动，在Chat做特殊处理
  postFaqAnswerFeedback: '/v1/faqchatcommsrv/feedback/faqid', // faq答案反馈
  isChinaMainland: '/v3/userserv/user/profile_service/get_user_category_info', // 判断是否是中国大陆用户
  checkHasOrder: '/v1/faqchatcommsrv/comm/has_order', // 判断用户是否有订单（登录态调用）
  getSingleOrderInfo: '/v1/usrcsrv/booking/list/by_order_nos', // 获取单个order的信息
  checkHasSubmitted: '/v1/faqchatcommsrv/comm/booking/:bookingNo/has_submit', // 检查是否已经提交过表单
  uploadAttachment: '/v1/faqchatcommsrv/comm/upload/mail/attachment', // 邮件上传图片附件
  sendEmailToCEG: '/v1/faqchatcommsrv/comm/mail/send', // 发送CEG邮件
  getMembershipCard: '/v1/mpsrv/product/card', // 请求MP业务的卡片
  getFullOrderDetail: '/v1/order/myorders/orderdetail', // 与订单详情一致的接口
  // 主题
  themepage: '/v1/usrcsrv/theme/detail', // 主题页内容
  // City 相关
  cityIndexComponent: '/v1/usrcsrv/jenga/page/city_page',
  homePageComponent: '/v1/usrcsrv/jenga/page/home_page',
  paymentPageComponent: '/v1/usrcsrv/jenga/page/payment_success_page',
  jengaPage: '/v1/usrcsrv/jenga/page/',
  cityActivitiesFilter: '/v3/usrcsrv/search/city/activities',
  updateAdBanner: '/v1/usrcsrv/get/city/banner/ad', // GET 登录态更新banner
  safeGuardTravelQuery: '/v1/usrcsrv/tw_covid19_campaign/query',

  // hotel
  hotelApiRefundReson: '/v1/hotelapiserv/order/refund/reason',
  hotelApiRefundPrice: '/v1/hotelapiserv/order/refund/price',
  hotelApiRefundApply: '/v1/hotelapiserv/order/refund/apply',
  hotelApiFilter: '/v2/hotelapiserv/hotelapi/filter/hotel',
  hotelApiItemsV1: '/v1/hotelapiserv/hotelapi/hotelItems',
  hotelApiItemsV2: '/v2/hotelapiserv/hotelapi/hotelItems',
  hotelApiSuggest: '/v2/hotelapiserv/hotelapi/suggest',
  hotelApiQuickFacts: '/v1/campaignapisrv/hubpage/quick_facts',
  hotelApiFaqs: '/v1/campaignapisrv/hubpage/faqs',
  hotelApiInternalLinks: '/v1/campaignapisrv/hubpage/internal_links',
  hotelOrderData: '/v3/hotelapiserv/hotelapi/order/portionData', // 根据order_guid 获取hotel checkin/out信息

  // GuestCheckout模式下发送的获取手机验证码
  guestSendSmsCodeWithCaptcha: '/v3/userserv/user/profile_service/send_sms_code_with_captcha',
  verifyGuestBookingSmsCode: '/v3/userserv/user/profile_service/verify_guest_booking_sms_code',

  // destination
  // getCitySummary: '/v1/campaignapisrv/destination/service/get_node_summary_by_city_country_idlist',
  getCitySummary: '/v1/campaignapisrv/destination/service/get_node_by_city_country_idlist',
  getCityImages: '/v1/campaignapisrv/destination/service/get_destination_image',
  getAllCountries: '/v1/experiencesrv/area/strative_area_service/get_all_countries',

  // iterableReport api
  iterable: '/v1/usrcsrv/iterable/event/track',

  // residence
  assumedResidence: '/v1/lbssrv/public/residence/assumed_residence',
  assumedResidencev2: '/v1/lbssrv/public/residence/assumed_residence_v2',

  // searchResult
  getSearchResultDetail: '/v1/cardinfocenterservicesrv/search/platform/complete_search_v3',
  getHotelTab: '/v1/cardinfocenterservicesrv/search/platform/hotel_tab',
  getPtpAladdinInfo: '/v1/cardinfocenterservicesrv/search/platform/srp_ptp_aladdin',
  getPtpAladdinPopularLocation: '/v1/cardinfocenterservicesrv/search/platform/srp_ptp_popular',
  getPtpAladdinSearchLocation: '/v1/cardinfocenterservicesrv/search/platform/srp_ptp_suggest'
  // getSearchResultDetail: 'https://b7069aa5-23ca-42ee-97a6-3d0643fc33a0.mock.pstmn.io/mock-search-filter'
}

/**
 * guest_checkout 需要代理的接口
 * registerGuest之后会设置cookie._pt_gc, 之后需要使用该cookie的接口需要node端代理反混淆并设置为_pt
 */
export const guestApiList =
  [apiNameMap.profile,
    apiNameMap.carRentalsAdd,
    apiNameMap.carRentalOrderDetail,
    apiNameMap.carRentalRefundReason,
    apiNameMap.carRentalGetAmount,
    apiNameMap.carRentalApplyAmount,
    apiNameMap.carRentalRefundDetail,
    apiNameMap.orderUserCancel,
    apiNameMap.registerGuest,
    apiNameMap.bookingNow,
    apiNameMap.addToShoppingCard,
    apiNameMap.getBookingList,
    apiNameMap.verify,
    apiNameMap.getRecentlyPurchase,
    apiNameMap.deleteBooking,
    apiNameMap.getLogistics,
    apiNameMap.deleteBooking,
    apiNameMap.cancelDeleteBooking,
    apiNameMap.stickBooking,
    apiNameMap.cancelStickBooking,
    apiNameMap.comboQuery,
    apiNameMap.bookingManagement,
    apiNameMap.submitBookingManagement,
    apiNameMap.revertBookingManagement,
    // hotel
    apiNameMap.hotelApiRefundReson,
    apiNameMap.hotelApiRefundPrice,
    apiNameMap.hotelApiRefundApply,
    hotelPayPreBook,
    hotelSettlement,
    hotelPayBook,
    apiNameMap.guestSendSmsCodeWithCaptcha,
    apiNameMap.verifyGuestBookingSmsCode,
    // CEG
    apiNameMap.chatAuth,
    apiNameMap.getOrderDetail,
    apiNameMap.isChinaMainland,
    apiNameMap.checkHasOrder,
    apiNameMap.getSingleOrderInfo,
    apiNameMap.checkHasSubmitted,
    apiNameMap.uploadAttachment,
    apiNameMap.sendEmailToCEG,

    // 订单详情
    apiNameMap.getBookingDetailSections,
    apiNameMap.getBookingPaymentDetail,
    apiNameMap.getElevyTravelers,

    // 退款相关
    apiNameMap.getBookingDetailsRefundAbleSkus,
    apiNameMap.calcBookingDetailsRefundAmount,
    apiNameMap.submitBookingDetailsRefund,

    // 自助修改订单
    apiNameMap.experienceManageBookingInfos,
    apiNameMap.experienceManageBookingRevert,
    apiNameMap.experienceManageBookingSubmit,
    apiNameMap.experienceManageBookingModify,

    // 支付成功页
    apiNameMap.paymentPageComponent,
    // iterable report
    apiNameMap.iterable,

    // guest user residence
    apiNameMap.assumedResidence,
    apiNameMap.assumedResidencev2,

    // 推荐活动楼层
    '/v1/cardinfocenterservicesrv/public/recommend/pay_success/page/recommend_acts_v2',
    // CampaignCoupon楼层
    '/v1/couponapisrv/order/campaign/result',
    // check-out-voucher
    '/v1/experiencesrv/dock/fast_pass_service/express_check_out_voucher'

  ]

export default apiNameMap
