// 合作伙伴定制数据
// @TODO: 这里有些凌乱，待梳理，规范字段

type IdNameMap = Record<string, string>
export const idNameMap: IdNameMap = {
  7383: 'qyer',
  8227: 'jinair',
  7772: 'samsung',
  6815: 'etips',
  10838: 'touring_pandas',
  9376: 'wego',
  8869: 'ktimeout',
  9199: 'yellow_balloon',
  8499: 'jr_pass_co',
  1080: 'hkexpress',
  13925: 'master_card',
  17381: 'kop_run'
}

const guest_checkout = {
  domain: 'https://guest.klook.com',
  util_type: 'guest_checkout',
  is_guest_checkout: 1,
  index_invite: 0,
  downland_bar: 0,
  user_center_path: 0,
  shopping_cart: 0,
  banner_ad: 0,
  hello_user_name: 0
}
type NameConfigMap = Record<string, any>
export const nameConfigMap: NameConfigMap = {
  default: {
    domain: 'https://www.klook.com',
    user_center_path: 1,
    hello_user_name: 1,
    index_invite: 1,
    footer: 1,
    downland_bar: 1,
    banner_ad: 1,
    faq_klook: 1,
    chat_with_klook: 1,
    use_credits: 1,
    pay_success_book_url: false,
    shopping_cart: 1,
    activityRelated: 1,
    activityBreadcrumb: 1,
    activityRecommend: 1,
    activityTop: 1,
    showSecondaryNav: 1,
    search: 1,
    booking_option_notice: 0,
    show_pkg_card_icons: 1,
    internalLink: 1,
    global_notice: 1
  },
  hkexpress: {
    util_name: 'hkexpress',
    css_style: 'hkexpress-theme'
  },
  agoda: {
    util_name: 'agoda'
  },
  touring_pandas: {
    util_name: 'touring_pandas'
  },
  qyer: {
    util_name: 'qyer'
  },
  wego: {
    util_name: 'wego'
  },
  jinair: {
    util_name: 'jinair'
  },
  samsung: {
    util_name: 'samsung'
  },
  etips: {
    util_name: 'samsung'
  },
  ktimeout: {
    util_name: 'ktimeout'
  },
  yellow_balloon: {
    util_name: 'yellow_balloon'
  },
  jr_pass_co: {
    util_name: 'jr_pass_co'
  },
  guest_checkout,
  srv_dts: {
    ...guest_checkout,
    showSecondaryNav: 0,
    search: 0,
    activityBreadcrumb: 0,
    activityRelated: 0,
    activityRecommend: 0,
    activityTop: 0,
    internalLink: 0
  },
  master_card: {
    util_name: 'master_card',
    pay_result_master_card_return_buttom: 1
  },
  kop_run: {
    util_name: 'kop_run',
    pay_result_kop_run_return_buttom: 1
  },
  express_check_out: {
    booking_option_notice: 1,
    show_pkg_card_icons: 0,
    f_source_type: 'express_check_out',
    order_sub_channel: 11,
    showSecondaryNav: 0,
    search: 0,
    footer: 0,
    noLinkToHomePageLogo: 1
  },
  experiences_white_label: {
    use_credits: 0,
    global_notice: 0
  }
}
