// return接口请求的loading config
export const getLoadingConfig = (
  text: string = '',
  width: string | number,
  src: string = 'http://res.klook.com/image/upload/v1639721175/cavobu4damovch1ztlcx.gif',
  size: string | number
) => ({
  text,
  showLoadingBg: true,
  contentWidth: width + 'px',
  render: src ? (h: any) => h('img', {
    slot: 'icon',
    attrs: { src },
    style: size ? {
      width: size + 'px',
      height: size + 'px'
    } : undefined
  }) : undefined
})
// hotel-api 接口
export const hotelDetailBasicInfo = '/v2/hotelapiserv/hotelapi/hotelInfo' // GET 酒店活动信息
export const hotelImageList = '/v1/hotelapiserv/hotelapi/hotelImgs'
export const hotelDetailRecommend = '/v1/hotelapiserv/hotelapi/recommendSimilarList' // GET 酒店推荐活动
export const hotelNearbyInfo = '/v1/hotelapiserv/hotelapi/hotelNearByInfo' // GET 酒店附近信息

// /v1/hotelapiserv/hotelapi/review/list

export const hotelReviewList = '/v1/hotelapiserv/hotelapi/review/list'
export const hotelReviewLast = '/v1/hotelapiserv/hotelapi/review/recommend?hotel_id='

export const hotelAddOnList = '/v3/hotelapiserv/hotelapi/addOn' // Add On模块数据

// order-settlement
export const hotelPayPreBook = '/v3/hotelapiserv/hotelapi/order/preBook' // 下单页主布局数据
export const hotelSettlement = '/v3/hotelapiserv/hotelapi/order/settlement'
export const hotelPayBook = '/v3/hotelapiserv/hotelapi/order/book'

export const hotelVoucherPayPreBook = '/v3/hotelapiserv/voucher/order/preBook' // 下单页主布局数据
export const hotelVoucherSettlement = '/v3/hotelapiserv/voucher/order/settlement'
export const hotelVoucherPayBook = '/v3/hotelapiserv/voucher/order/book'
export const hotelCouponList = '/v3/hotelapiserv/coupon/list'
export const hotelCouponRedeem = '/v3/hotelapiserv/coupon/redeem'

export const addWishlist = '/v1/hotelapiserv/wish/hotel/add'
export const cancelWishlist = '/v1/hotelapiserv/wish/hotel/cancel'
export const verticalBasicInfo = '/v2/hotelapiserv/hotelapi/vertical_page'

// 无日期酒店详情
export const noDateRoomList = '/v4/hotelapiserv/hotelapi/rate/room/list'
export const rateRoomCalendar = '/v4/hotelapiserv/hotelapi/rate/room/calendar/list'

export default {
  getVoucherOrderDetail: '/v2/hotelapiserv/voucher/orderDetail',
  getVoucherPaymentDetail: '/v2/hotelapiserv/voucher/paymentDetail'
}
