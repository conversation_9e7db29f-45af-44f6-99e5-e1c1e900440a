import { Context } from 'egg'
import Axios from 'axios'
import { randomString } from '../utils'
import logger from '../logger'
import { convertToBackendCode } from '../data/language'
import { formatError, formatReq, formatRes } from '../axios/utils'
import cookies from '../data/cookies'
import { guestApiList } from '../data/apis'
import { match } from '../api.config'

export function createAxios(ctx: Context) {
  const axios = Axios.create({
    baseURL: ctx.app.config.envConfig.API_URL,
    timeout: 6000,
    withCredentials: false,
    headers: {
      Version: '5.6',
      Accept: 'application/json, text/plain, */*',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })

  axios.CancelToken = Axios.CancelToken
  axios.isCancel = Axios.isCancel

  // Setting request hook
  axios.interceptors.request.use(
    function requestSuccess(config) {
      const { headers } = config

      // Setting custom header
      headers['Accept-Language'] = convertToBackendCode(ctx.language?.language as Data.Language) || ''
      headers.Currency = ctx.currency?.currency || ''
      headers.Token = headers.token || ctx.cookies.get(cookies.token.key) || ''
      headers._pt = ctx.cookies.get(cookies.deviceId.key) || ''
      headers['X-Klook-Kepler-Id'] = ctx.keplerId || ''
      headers['X-Klook-Page-Open-Id'] = ctx.req.pageId || ''
      headers['X-Klook-Host'] = ctx.realHost || ''

      // residence
      if (ctx.req.residenceId) {
        headers['X-Klook-User-Residence'] = `${ctx.req.residenceId}_${ctx.req.residenceCode}`
      }

      // Change platform if is access from app
      if (ctx.deviceInfo.isKlookApp) {
        if (ctx.deviceInfo.isOpenHarmony) {
          // @ts-ignore
          headers['X-Platform'] = headers['X-Platform'] || ctx.deviceInfo.xplatform || ''
        } else {
          headers['X-Platform'] = headers['X-Platform'] || (ctx.deviceInfo.isIOS ? 'ios' : 'android')
        }
      } else {
        headers['X-Platform'] = headers['X-Platform'] || ctx.deviceInfo.platform || ''
      }

      headers['X-Klook-Market'] = ctx?.websiteConfig?.market || 'global'

      config.baseURL = match(config.url!)

      // 开发环境取到的ip是127.0.0.1
      const env = process.env.NODE_ENV || 'development'
      if (env !== 'development') {
        // 设置 x-forwarded-for 转发客户端ip
        const xForwardedForHealthCheck = ctx.get('x-forwarded-for-health-check')

        const realIp = ctx.get('X-Real-IP')
        const ips = ctx.ips
        const ipsStr = Array.isArray(ips) && ips.length ? ips.join(',') : ''

        headers['X-Forwarded-For'] = xForwardedForHealthCheck || ipsStr || ctx.ip || '0.0.0.0'
        headers['X-Real-IP'] = realIp || ctx.ip || '0.0.0.0'
      }

      headers['X-REQ-CLIENT'] = 'klook-nuxt-web'
      headers.Referer = ctx.get('referer') || ''
      headers['User-Agent'] = ctx.get('user-agent') || ''
      headers.Cookie = ctx.req.headers.cookie || ''
      headers['X-DeviceID'] = ctx.get('x-deviceid') || ''
      headers['X-Request-From'] = 'node'

      // change prefix of the api if it is guest checkout mode
      if (ctx.utilConfig?.is_guest_checkout && guestApiList.includes(config.url || '')) {
        config.baseURL = `http://127.0.0.1:${ctx.req.socket.localPort}/xos_gc`
      }

      // Internal fields
      config.startTime = Date.now()
      config.fullURL = (config.baseURL || '').replace(/\/$/, '') + config.url
      config.requestId = process.server
        ? `${ctx.requestId}-${randomString()}`
        : randomString()

      logger.info(formatReq({
        requestId: config.requestId || '',
        method: config.method!,
        url: config.fullURL
      }))

      return config
    },
    function requestError(error) {
      return Promise.reject(error)
    }
  )

  // Setting response hook
  axios.interceptors.response.use(
    function responseSuccess(response) {
      const { data, config, status } = response

      if (data) {
        const success = data.success
        ctx.logquery?.service({
          level: 'I',
          isMasked: true,
          file: __filename,
          funcName: 'axios-guest-success',
          message: {
            href: ctx.url,
            success,
            requestId: config.requestId! || '',
            method: config.method!,
            url: config.fullURL!,
            status: String(status),
            error: success ? '' : data.error,
            elapsedTime: Date.now() - config.startTime!
          }
        })
      }

      if (typeof data === 'object' && !data.success) {
        data.error = data.error || {
          code: '',
          message: 'server_request_failure'
        }

        if (config.throwError) {
          const { code, message, ...others } = data.error
          const error = new Error(message)
          const errorType = 'service'

          if (process.server) {
            logger.error(formatError({
              requestId: config.requestId! || '',
              method: config.method!,
              url: config.fullURL!,
              status: code,
              elapsedTime: Date.now() - config.startTime!,
              message
            }))
          }

          return Promise.reject(Object.assign(error, {
            ...others,
            code,
            data,
            config,
            errorType
          }))
        }
      }

      logger.info(formatRes({
        requestId: config.requestId! || '',
        method: config.method!,
        url: config.fullURL!,
        status: String(status),
        elapsedTime: Date.now() - config.startTime!
      }))

      return response
    },
    function responseError(error) {
      const { message, config, response, request } = error

      const errorInfo = formatError({
        requestId: config?.requestId || '',
        method: config?.method || '',
        url: config?.fullURL,
        status: error.code,
        elapsedTime: Date.now() - config?.startTime!,
        message
      })

      ctx.logquery?.service({
        level: 'E',
        file: __filename,
        isMasked: true,
        funcName: 'axios-guest',
        message: {
          response: !!response,
          href: ctx.url,
          host: ctx.get('host'),
          ua: ctx.get('user-agent'),
          errorInfo
        }
      })

      if (response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const { data, status } = response

        error.errorType = 'network'
        error.code = (data.error && data.error.code) || String(status)
        error.message = (data.error && data.error.message) || 'server_request_failure'
      } else if (request) {
        // The request was made but no response was received
        // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
        // http.ClientRequest in node.js
        error.code = '990001'
        error.errorType = 'client'
        error.message = 'server_request_failure'
      } else {
        // Something happened in setting up the request that triggered an Error
        error.code = '990001'
        error.errorType = 'client'
        error.message = 'server_request_failure' // Overwrite actual message
      }

      logger.error(formatError({
        requestId: config?.requestId || '',
        method: config?.method || '',
        url: config?.fullURL,
        status: error.code,
        elapsedTime: Date.now() - config?.startTime!,
        message
      }))

      if (config?.ignoreError) {
        return { data: { error } }
      }

      // eslint-disable-next-line no-console
      console.log(error)

      return Promise.reject(error)
    }
  )

  return axios
}

export default function genAxios() {
  return async (ctx, next) => {
    ctx.req.axios = createAxios(ctx)

    await next()
  }
}
