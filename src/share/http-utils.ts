/**
 * Created by <PERSON><PERSON>Jun on 2021/4/25 10:27.
 */

type AxiosResp = Promise<any>

// 对axios接口返回值做一致性处理
// 避免修改拦截影响老代码 所以通过手动调用该方法
export function normalizeAxiosResponse(axiosResp: AxiosResp): Promise<{
  success: boolean,
  error: {
    code: number,
    message: string
  },
  result: any
}> {
  return axiosResp.then((res) => {
    // 不返回内容
    if (!res) {
      return {
        success: false,
        error: { code: 404 }
      }
    }

    // 其它错误一律按 404处理
    if (!res.success) {
      return {
        success: false,
        error: {
          code: 404,
          message: res.result?.error?.message
        }
      }
    }

    return res
  }).catch((e) => {
    return {
      success: false,
      error: {
        code: 404,
        message: e?.message || e
      }
    }
  })
}
