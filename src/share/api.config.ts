
const configList = [
  {
    test: /\/v[\d]\/promosrv\//,
    host: 'http://promosrv.klook-promo:8080'
  },
  {
    test: /\/v[\d]\/userserv\//,
    host: 'http://userserv.klook-user:8080'
  },
  {
    test: /\/v[\d]\/usrcsrv\//,
    host: 'http://usrcommsrv.klook-appapi:8080'
  },
  {
    test: /\/v[\d]\/mpsrv\//,
    host: 'http://mpsrv.klook-membership:8080'
  },
  {
    test: /\/v[\d]\/websrv\//,
    host: 'http://websrv.klook-appapi:8080'
  },
  {
    test: /\/v[\d]\/order\//,
    host: 'http://appserv.klook-order:8080'
  },
  {
    test: /\/v[\d]\/fnbapisrv\//,
    host: 'http://fnbapisrv.klook-fnb:8080'
  },
  {
    test: /\/v[\d]\/fnbvouchersrv\//,
    host: 'http://fnbvouchersrv.klook-fnb:8080'
  },
  {
    test: /\/v[\d]\/affinternalsrv\//,
    host: 'http://affinternalsrv.klook-partnership:8080'
  },
  {
    test: /\/v[\d]\/experiencesrv\//,
    host: 'http://experiencesrv.klook-experience:8080'
  },
  {
    test: /\/v[\d]\/reappserv\//,
    host: 'http://reappserv.klook-raileurope:8080'
  },
  {
    test: /\/v[\d]\/transfercarrentalapisrv\//,
    host: 'http://transfercarrentalapisrv.klook-carrental:8080'
  },
  {
    test: /\/v[\d]\/redeemcodesrv\//,
    host: 'http://redeemcodesrv.klook-partnership:8080'
  },
  {
    test: /\/v[\d]\/zendeskgwsrv\//,
    host: 'http://zendeskgwsrv.klook-chatserver:8080'
  },
  {
    test: /\/v[\d]\/bookingapiserv\//,
    host: 'http://bookingapiserv.klook-order:8080'
  },
  {
    test: /\/v[\d]\/search\//,
    host: 'http://usrcommsrv.klook-appapi:8080'
  },
  {
    test: /\/v[\d]\/hotelapiserv\//,
    host: 'http://hotelapiserv.klook-hotel:8080'
  },
  {
    test: /\/v[\d]\/gatewaysrv\//,
    host: 'http://wwwgateway.klook-gateway:8080'
  },
  {
    test: /\/v[\d]\/enteventapisrv\//,
    host: 'http://enteventapisrv.klook-event:8080'
  },
  {
    test: /\/v[\d]\/locpublishserv\//,
    host: 'http://locpublishserv.klook-localisationcms:8080'
  },
  {
    test: /\/v[\d]\/cardinfocenterservicesrv\//,
    host: 'http://cardinfocenterservicesrv.klook-cardinfocenterservice:8080'
  },
  {
    test: /\/v[\d]\/searchapisrv\//,
    host: 'http://searchapisrv.klook-searchapi:8080'
  },
  {
    test: /\/v[\d]\/campaignapisrv\//,
    host: 'http://campaignapisrv.klook-mcp:8080'
  },
  {
    test: /\/api\/timestamp/,
    host: 'http://newwebnode.klook-new-web:8080'
  },
  {
    test: /\/v[\d]\/lbssrv\//,
    host: 'http://lbssrv.klook-lbs:8080'
  },
  {
    test: /\/v[\d]\/seosrv\//,
    host: 'http://seosrv.klook-seo:8080'
  }
]

export function match(path?: string) {
  const default_host = process.env.API_URL
  const app_env = process.env.APP_ENV

  if (!path) { return default_host }

  if (app_env === 'development') {
    return default_host
  }

  const config = configList.find(i => i.test.test(path))
  return config?.host || default_host
}
