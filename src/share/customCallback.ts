
import { Context } from '@midwayjs/web'

export type CallBackReturn = {
  enable: boolean
  feature?: string
}

const cacheHost = [
  'www.klook.com',
  'www.klook.cn',
  'www.stage.klook.io', // stage环境测试需要所以同样也进入缓存
  'www.stage.klook.cn'
  // '127.0.0.1:3000' // for localhost testing
]

export interface CallBack { (ctx: Context, useWhiteList?: boolean): CallBackReturn}

export const useResidenceCallBack: CallBack = function (ctx) {
  // 判断是否是缓存域名
  if (!cacheHost.includes(ctx.host)) {
    return {
      enable: false
    }
  }

  const whiteList = [
    'zh-CN:CN:CNY',
    'en-SG:SG:SGD',
    'en:SG:SGD',
    'zh-TW:TW:TWD',
    'zh-HK:HK:HKD',
    'en-HK:HK:HKD',
    'en-MY:MY:MYR',
    'ko:KR:KRW',
    'th:TH:THB',
    'vi:VM:VND',
    'en-PH:PH:PHP'
    // 'zh-CN:BS:CNY' // for localhost testing
  ]
  const feature = `${ctx.language.language}:${ctx.req.residenceCode}:${ctx.currency.currency}`
  if (whiteList.includes(feature)) {
    return {
      enable: true,
      feature
    }
  }

  return {
    enable: false
  }
}

export const usePartResidenceCallback: CallBack = function (ctx, useWhiteList = true) {
  // 判断是否是缓存域名
  if (!cacheHost.includes(ctx.host)) {
    return {
      enable: false
    }
  }

  const whiteList = [
    'zh-CN:CN:CNY:CN',
    'en-SG:SG:SGD:SG',
    'en:SG:SGD:SG',
    'en:US:USD:US',
    'zh-TW:TW:TWD:TW',
    'zh-HK:HK:HKD:HK',
    'en-HK:HK:HKD:HK',
    'en-MY:MY:MYR:MY',
    'ko:KR:KRW:KR',
    'th:TH:THB:TH',
    'vi:VM:VND:VM',
    'en-PH:PH:PHP:PH'
    // 'zh-CN:BS:CNY:CN' // for localhost testing
  ]

  // 判断用户不做缓存
  // csr 模式不做缓存
  const isLimitedAccess = (ctx.req.userInfo?.restrict_policies || []).includes('deprecated')
  if (isLimitedAccess || ctx.useCsrRender) {
    return {
      enable: false
    }
  }
  const feature = `${ctx.language?.language}:${ctx.country}:${ctx.currency?.currency}:${ctx.req.residenceCode}`

  // 白名单开启
  return {
    enable: useWhiteList ? whiteList.includes(feature) : true,
    feature
  }
}
