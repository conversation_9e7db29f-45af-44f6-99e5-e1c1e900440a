import { request } from 'http'
import dayjs from 'dayjs'

const nodeOnlyLarkNotify = (body: Object) => new Promise((resolve) => {
  if (!body) {
    return
  }

  const r = request('http://node.klook-web-bff:8080/v1/webbffapi/webhooks/serviceLarkAlert', {
    headers: {
      'Content-Type': 'application/json'
    },
    method: 'POST'
  }, (res) => {
    res.on('error', () => resolve(null))
  })
  r.on('error', () => resolve(null))
  r.write(JSON.stringify(body))
  r.end()
})

const webhookUrl = 'https://open.larksuite.com/open-apis/bot/v2/hook/9eb9f4e5-79ce-4ad6-98fc-db1790d57466'

const commonTags = [
  { label: '服务', value: 'ssrinsurance' },
  { label: 'Pod', value: process.env.HOSTNAME || process.env.POD_NAME || '-' },
  { label: 'Env', value: process.env.KL_ARGS_ENV || process.env.KL_ARGS_PF_ENV || '-' },
  { label: 'Site', value: process.env.KL_ARGS_SITE || process.env.KL_ARGS_K8S_NAME || '-' }
]

export const createCsrFallbackNotify = (message: string, level?: 0 | 1) => {
  let count = 0

  // curQps === 1 时，标识新的qps周期
  return (isActive: boolean, curQps: number, maxQps?: number) => {
    if (count > 0 && curQps === 1) {
      count--

      // 告警恢复
      if (count === 0) {
        nodeOnlyLarkNotify({
          title: `【告警已恢复】${message}`,
          webhook: webhookUrl,
          project: 'klook-ssrinsurance',
          tags: [
            { label: 'QPS(单worker)', value: `${curQps} (max: ${maxQps || '-'})` },
            { label: '时间', value: dayjs(new Date()).format() },
            ...commonTags
          ]
        })
      }
    }

    if (isActive) {
      // 告警产生
      if (count === 0) {
        nodeOnlyLarkNotify({
          title: `【告警产生】${message}`,
          level,
          webhook: webhookUrl,
          project: 'klook-ssrinsurance',
          tags: [
            { label: 'QPS(单worker)', value: `${curQps} (max: ${maxQps || '-'})` },
            { label: '时间', value: dayjs(new Date()).format() },
            ...commonTags
          ]
        })
      }

      count = 6
    }
  }
}
