import { Inject, Provide } from '@midwayjs/decorator'
import { Context } from '@midwayjs/web'
import { IVueRenderService } from '../interface'

@Provide('VueRenderService')
export class VueRenderService implements IVueRenderService {
  @Inject()
    ctx: Context

  async render(): Promise<any> {
    // 注入client __conf_env
    const ctx = this.ctx
    if (ctx.useCsrRender) {
      ctx.setClientState({
        country: ctx.country,
        residenceCode: ctx.req.residenceCode,
        residenceId: ctx.req.residenceId,
        languageInfo: ctx.language,
        affiliateConf: ctx.affiliate,
        currencyInfo: ctx.currency,
        featureInfo: ctx.features,
        deviceInfo: ctx.deviceInfo,
        websiteConfig: ctx.websiteConfig,
        utilConfig: ctx.utilConfig,
        keplerId: ctx.keplerId,
        realHost: ctx.realHost
      })
    }

    // inject client config env
    ctx.injectHeadMeta(`<script>(function(w,d){w.__conf_env=${ctx.clientConfString};var cs=d.currentScript;(cs&&cs.parentNode&&cs.parentNode.removeChild(cs))})(window,document)</script>`)

    ctx.injectHeadMeta('<!--<headerSlot>-->')

    // csr
    if (ctx.useCsrRender) {
      ctx.body = await ctx.renderCsr()
      return
    }

    // ssr
    const renderStart = Date.now()
    await ctx.renderSsr().then((res) => {
      ctx.body = res
      ctx.logquery?.general({
        level: 'I',
        isMasked: true,
        message: {
          subtype: 'node-request-timing',
          timing: {
            url: ctx.url,
            host: ctx.host,
            keplerId: ctx.keplerId,
            renderTime: Date.now() - renderStart,
            requestTime: Date.now() - ctx.starttime
          }
        }
      })
    })
      .catch((error) => {
        ctx.status = ctx.ssrError?.statusCode || 500
        ctx.logger.error(error)

        ctx.logquery?.service({
          level: 'W',
          file: __filename,
          isMasked: true,
          funcName: 'ssr-render-error',
          message: {
            host: ctx.host,
            url: ctx.url,
            keplerId: ctx.keplerId,
            ua: ctx.get('user-agent'),
            stack: error.stack,
            errMsg: error.message
          }
        })
        if (['development', 'local'].includes(ctx.app.config.env)) {
          throw error
        } else {
          return ctx.service.staticPage.renderStaticPage(ctx.status.toString())
        }
      })
  }
}
