import { Configuration, App } from '@midwayjs/decorator'
import * as web from '@midwayjs/web'
import * as klook from '@klook/midway-web'
import type { Application } from 'egg'

import { NoindexMiddlewareGeneral } from '@klook/middleware-noindex'
import { getKoaMiddleware } from '@klook/optimus-base'
import { ComposeMiddleware } from './middleware/compose.middleware'
import { LanguageRedirectMiddleware } from './middleware/language.redirect'
import { GenMiddleware } from './middleware/gen.middleware'
import { WebsiteMiddleware } from './middleware/website.middleware'
import { UtilConfigMiddleware } from './middleware/utilconfig.middleware'
import { RedirectToCnMiddleware } from './middleware/redirectToCn.middleware'
import { CityMiddleware } from './middleware/cityredirect.middleware'
import { PoiMiddleware } from './middleware/poiredirect.middleware'
import { HeaderMiddleware } from './middleware/header.middleware'
import initialMiddleware from './middleware/initialMiddleware'

import proxies from './util/proxies'
import { NotFoundFilter } from './filter/notfound.filter'
import { DefaultErrorFilter } from './filter/default.filter'

const { normalizePath, commonService } = require('@klook/klook-node-framework/lib/commonMiddleware')

@Configuration({
  imports: [web, klook]
})
export class ContainerLifeCycle {
  @App()
    app!: web.Application & Application

  onReady() {
    this.app.use(
      normalizePath({
        enable: true,
        blackList: [ctx => /(^\/v\d\/)|(\.png$|\.js$|\.css$)/.test(ctx.path)]
      })
    )
    this.app.useFilter([NotFoundFilter, DefaultErrorFilter])

    proxies(this.app)

    this.app.use(initialMiddleware())
    this.app.use(
      commonService({
        experiment: {
          mobile: {
            enable: true,
            experimentList: []
          },
          desktop: {
            enable: true,
            experimentList: []
          }
        },
        affiliate: {
          enable: true,
          autoRedirect: true
        }
      })
    )

    const noIndexQueryKey = this.app.getConfig('noIndexQueryKey')
    this.app.useMiddleware([
      GenMiddleware,
      ComposeMiddleware,
      WebsiteMiddleware,
      UtilConfigMiddleware,
      RedirectToCnMiddleware,
      CityMiddleware,
      PoiMiddleware,
      LanguageRedirectMiddleware,
      HeaderMiddleware,
      getKoaMiddleware(),
      NoindexMiddlewareGeneral({
        noIndexQueryKey,
        enable: true
      })
    ])
  }
}
