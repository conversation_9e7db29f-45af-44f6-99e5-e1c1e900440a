import { join } from 'path'
import { EggAppConfig, EggAppInfo, PowerPartial } from 'egg'
import { createCsrFallbackNotify } from '../share/lark'
import kvConfig from './klook.kv.config'

export type DefaultConfig = PowerPartial<EggAppConfig>

// 不完全降级通知
const csrNotify = createCsrFallbackNotify('首页CSR降级(QPS)', 1)
// 完全降级告警通知
const csrFullNotify = createCsrFallbackNotify('首页完全CSR降级(QPS)', 0)

export default (appInfo: EggAppInfo) => {
  const config = {} as DefaultConfig

  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + '_1611038425326_4049'
  // add your config here
  config.middleware = []
  config.static = {
    prefix: '/',
    dir: join(appInfo.baseDir, './build')
  }

  config.logquery = kvConfig.logquery
  // static page
  config.staticPage = {
    urlOrigin: 'https://www.klook.com'
  }

  if (kvConfig.redis) {
    config.redis = {
      client: {
        ...kvConfig.redis.client,
        keyPrefix: 'insurance:' // 根据业务域修改
      }
    }
  }

  if (kvConfig.site) {
    config.site = kvConfig.site
  }

  if (kvConfig.xtransit) {
    config.xtransit = kvConfig.xtransit
  }

  config.experiment = {
    serverUrl: 'http://keplersrv.klook-appapi:8080/v1/keplersrv/public/hit/experiments'
  }

  config.optimise = {
    auto: true
  }

  config.onerror = {
    errorPageUrl: '/500'
  }

  config.notfound = {
    pageUrl: '/404'
  }

  config.lru = {
    app: true,
    clients: {
      cacheHtml: {
        max: 100
      },
      memCache: {
        max: 150,
        maxAge: 1000 * 60 * 20,
        maxSize: 300
      }
    }
  }

  config.cookies = {
    signed: false
  }

  config.envConfig = kvConfig.API_URL || 'http://elb-apiv2.klook.io'

  config.midwayLogger = {
    clients: {
      appLogger: {
        level: 'info'
      }
    },
    default: {
      level: 'info'
    }
  }

  config.device = {
    staticOrApiRules: [
      /\.[a-z]{2,4}$|__webpack_hmr/
    ]
  }

  const homepageReg = new RegExp('^/(|en-MY/|en/|en-HK/|ms-MY/|ko/|en-SG/|en-US/|zh-CN/|zh-HK/|zh-TW/|ja/|th/|en-PH/|de/|en-AU/|en-CA/|en-GB/|en-IN/|en-NZ/|es/|fr/|id/|it/|ru/|vi/)$')
  config.guardian = {
    clients: {
      http: {
        // rules: ['WorkerQueryCsrRule', 'WorkerQueryFullCsrRule', 'WorkerQpsCsrRule', 'WorkerQpsFullCsrRule']
        rules: [
          ...(kvConfig.guardian?.debug || process.env.NODE_ENV === 'development') ? ['WorkerQueryCsrRule', 'WorkerQueryFullCsrRule'] : [],
          ...kvConfig.guardian?.homePageCsrQps ? ['WorkerQpsCsrRule'] : [],
          ...kvConfig.guardian?.homePageFullCsrQps ? ['WorkerQpsFullCsrRule'] : []
        ]
      }
    },
    rules: {
      // 首页测试配置
      WorkerQueryCsrRule: {
        validator: 'WorkerQueryValidator',
        interrupter: 'CsrInterrupter',
        csrCtxFlagKey: 'guardianCsrFlag',
        pathReg: homepageReg,
        queryKey: '_csr1'
      },
      // 首页测试配置
      WorkerQueryFullCsrRule: {
        validator: 'WorkerQueryValidator',
        interrupter: 'FullCsrInterrupter',
        pathReg: homepageReg,
        queryKey: '_full_csr'
      },

      // 首页 n 个并发 不降级csr
      WorkerQpsCsrRule: {
        validator: 'WorkerQpsValidator',
        interrupter: 'CsrInterrupter',
        csrCtxFlagKey: 'guardianCsrFlag',
        thresholdList: [
          {
            key: 'home-page-csr',
            path: homepageReg,
            qps: kvConfig.guardian?.homePageCsrQps
          }
        ],
        printLog(_ctx, isValid, qps, _key) {
          csrNotify(!isValid, qps, kvConfig.guardian?.homePageCsrQps)
          // logquery 告警等等配置这里
          // console.log('isValid csr', isValid, key, qps, kvConfig.guardian?.homePageCsrQps, ctx.url || ctx.path, ctx.method)
        }
      },

      // 首页 n 个并发 完全csr
      WorkerQpsFullCsrRule: {
        validator: 'WorkerQpsValidator',
        interrupter: 'FullCsrInterrupter',
        thresholdList: [
          {
            key: 'home-page-full-csr',
            path: homepageReg,
            qps: kvConfig.guardian?.homePageFullCsrQps
          }
        ],
        printLog(_ctx, isValid, qps, _key) {
          csrFullNotify(!isValid, qps, kvConfig.guardian?.homePageFullCsrQps)
          // logquery 告警等等配置这里
          // console.log('isValid full csr', isValid, key, qps, kvConfig.guardian?.homePageCsrQps, ctx.url || ctx.path, ctx.method)
        }
      }
    }
  }
  config.noIndexQueryKey = kvConfig.noIndexQueryKey || ['aid']
  console.log('__current_server_config__', config)
  return config
}
