import { EggAppConfig, PowerPartial } from 'egg'

export type DefaultConfig = PowerPartial<EggAppConfig>
const host = 'https://t48.fat.klook.io'

export default () => {
  const config: DefaultConfig = {}

  config.experiment = {
    serverUrl: 'https://uat2.fat.klook.io/v1/keplersrv/public/hit/experiments'
  }

  config.keys = '123456'

  config.redis = {
    client: {
      host: '127.0.0.1',
      db: 3,
      port: 6379,
      password: ''
    }
  }

  config.axios = {
    baseURL: host
  }

  config.logquery = {
    app: false,
    agent: false,
    console: false,
    client: {
      url: 'nats://localhost:4444',
      dev: true,
      subject: 'log.go.frontend.klook-nuxt-web.node',
      serviceName: 'klook-nuxt-web/node',
      type: 'none'
    }
  }

  config.affiliate = {
    landingUrl: `${host}/v1/affinternalsrv/get_landing_url`,
    serverUrl: `${host}/v1/affinternalsrv/aid2uid`
  }

  config.envConfig = {
    API_URL: host
  }

  // ...
  return config
}
