import { getCurrentConfig } from '@klook/kv'

const parseData = (k: string) => {
  try {
    return JSON.parse(k)
  } catch (_e) {
    return null
  }
}

const getKvData = () => {
  const data = getCurrentConfig() || {}

  return {
    API_URL: data.API_URL || '',
    redis: parseData(data['klook.redis.cluster']),
    logquery: parseData(data.SERVER_LOGQUERY),
    xtransit: parseData(data['klook.xtransit']),
    site: parseData(data['klook.site']),
    noIndexQueryKey: parseData(data['klook.noIndexQueryKey']),
    guardian: parseData(data['klook.guardian'])
  }
}

const klookConfig = getKvData()

export default klookConfig
