import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'

@Middleware()
export class UtilConfigMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      if (ctx.req.isStaticOrApi) {
        await next()
        return
      }

      const { utilConfig } = ctx

      ctx.req.utilConfig = utilConfig

      await next()
    }
  }

  static getName(): string {
    return 'utilconfig'
  }
}
