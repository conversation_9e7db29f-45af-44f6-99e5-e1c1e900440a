import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'

@Middleware()
export class CityMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      if (ctx.query.__qcache) {
        const memCache = ctx.app.lru.get('memCache')
        const cacheHtml = ctx.app.lru.get('cacheHtml')
        ctx.logquery?.service({
          level: 'I',
          funcName: __filename,
          isMasked: true,
          message: {
            tag: 'lru-info',
            memCache: {
              keys: memCache.keys(),
              length: memCache.length,
              itemCount: memCache.itemCount
            },
            cacheHtml: {
              keys: cacheHtml.keys(),
              length: cacheHtml.length,
              itemCount: cacheHtml.itemCount
            }
          }
        })
      }

      const redirect = (rPath: string, isFullUrl: boolean = false) => {
        ctx.status = 301
        return ctx.redirect(isFullUrl ? rPath : ctx.path.replace(regCity, rPath))
      }

      const qs = (query: Record<string, string | number>) => {
        return Object.entries(query).map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`).join('&')
      }

      // city vacation 页面跳转回旧地址
      if (/\/city\/\d+([^/]+)?\/vacation\//.test(ctx.path)) {
        return redirect(ctx.url.replace('/vacation/', '/'), true)
      }

      /* 匹配city页
      * match: /city/2-hong-kong-things-to-do/ 或 /city/2-hong-kong-things-to-do 或 /city/2
      * noMatch: /city/2-hong-kong-things-to-do/xxx
      * */
      const regCity = /\/city\/(\d+)([^/]+)?\/?$/
      const matches = ctx.path.match(regCity)

      // 非城市页return
      if (!matches || !matches[1]) {
        await next()
        return
      }

      const cityId = +matches[1]
      const specialCityIdMapping: Record<string, string> = {
        // 100 日本铁路
        100: '/japan-rail-jr-pass/',

        // 国内铁路
        288: '/china-train-ticket/'
      }

      // 检查 city_id 是不是数字
      if (isNaN(cityId)) {
        return redirect('/404/')
      }

      // 检测是否特殊 cityId
      if (specialCityIdMapping[cityId]) {
        return redirect(specialCityIdMapping[cityId])
      }

      // query中存在currency和当前货币不一致，需要重定向到一致，并且删除 price_from, price_to
      // 兼容城市页下活动过滤通过url获取货币的逻辑
      if (ctx.query && ctx.query.currency && (ctx.query.currency !== ctx.currency.currency)) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { price_from, price_to, currency, ...query } = ctx.query

        return redirect(`${ctx.path}?${
          qs({
            ...query,
            currency: ctx.currency.currency
          })
        }`, true)
      }

      // 判断 query 是否包含 template_ids, tag_ids 做移除重定向
      if (ctx.query.template_ids || ctx.query.tag_ids) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { template_ids, tag_ids, ...query } = ctx.query
        const newQs = qs(query as Record<string, string>)
        return redirect(`${ctx.path}${newQs ? '?' : ''}${newQs}`, true)
      }
      return await next()
    }
  }

  match(ctx: Context<unknown>) {
    return !ctx.isStaticOrApi && /city/.test(ctx.path)
  };
}
