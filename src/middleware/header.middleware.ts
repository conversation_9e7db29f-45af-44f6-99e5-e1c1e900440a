import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'

@Middleware()
export class HeaderMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      ctx.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
      return await next()
    }
  }
}
