import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'
import uuid from 'uuid/v4'

function randomString() {
  return uuid().slice(0, 8)
}

@Middleware()
export class GenMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      ctx.req.requestId = randomString()

      ctx.req.isStaticOrApi = ctx.isStaticOrApi

      ctx.req.pageId = uuid()
      return await next()
    }
  }

  static getName(): string {
    return 'cacheHtml'
  }
}
