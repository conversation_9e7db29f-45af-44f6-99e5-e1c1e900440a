import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'
import Lru from 'lru-cache'

declare module 'egg' {
  interface Application {
    lru: Lru & <PERSON>ton<Lru>;
  }
}

@Middleware()
export class ComposeMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      ctx.req.memCache = ctx.app.lru.get('memCache')

      ctx.req.country = ctx.country as Data.Country
      Object.assign(ctx.req, ctx.features)
      Object.assign(ctx.req, ctx.language)
      Object.assign(ctx.req, ctx.currency)
      Object.assign(ctx.req, ctx.deviceInfo)
      Object.assign(ctx.req, ctx.experiment)
      ctx.req.affiliateConf = ctx.affiliate
      ctx.req.realHost = ctx.realHost
      ctx.req.keplerId = ctx.keplerId
      ctx.req.ip = ctx.ip
      ctx.req.ips = ctx.ips
      ctx.req.websiteConfig = ctx.websiteConfig
      ctx.userInfo = ctx.req.userInfo

      ctx.req.trafficState = ctx.trafficState

      if (!ctx.req.logquery && ctx.logquery) {
        ctx.req.logquery = ctx.logquery
      }

      return await next()
    }
  }

  static getName(): string {
    return 'compose-middleware'
  }
}
