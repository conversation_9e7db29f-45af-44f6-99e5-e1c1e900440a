import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'
import { languageConfig } from '@klook/site-config'
import qs from 'qs'

@Middleware()
export class LanguageRedirectMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    const allLanguageReg = new RegExp(`^/(${languageConfig.supportLanguageList.join('|')})/`)

    return async (ctx: Context, next) => {
      if (ctx.isStaticOrApi) {
        return await next()
      }
      const querystring = qs.stringify(ctx.query)
      const defaultLanguage = ctx.websiteConfig.default_languages || 'en'
      const languages = languageConfig.getSupportLanguageList(ctx.websiteConfig.website).filter(lang => lang !== 'en')
      const languageReg = new RegExp(`^/(${languages.join('|')})/`)

      if (ctx.path === '/') {
        const cookieLanguage = ctx.cookieLanguage as Data.Language | undefined
        if (cookieLanguage) {
          if (cookieLanguage !== 'en') {
            const redirectUrl = querystring ? `/${cookieLanguage}/?${querystring}` : `/${cookieLanguage}/`
            return ctx.redirect(redirectUrl)
          }
          await next()
          return
        }

        // Default
        if (defaultLanguage !== 'en') {
          return ctx.redirect(`/${defaultLanguage}/`)
        }
      }

      // Others
      const matches = ctx.path.match(languageReg)
      const urlLanguage = matches && matches[1]

      const matchesAll = ctx.path.match(allLanguageReg)

      if (urlLanguage) {
        ctx.req.language = urlLanguage as Data.Language
        return await next()
      }

      if (!urlLanguage && matchesAll && matchesAll[1]) {
        // 处理en跳转的情况
        const rediretUrl = ctx.websiteConfig.default_languages === 'en' ? '/' : `/${ctx.websiteConfig.default_languages}/`
        return ctx.redirect(rediretUrl)
      }

      if (defaultLanguage !== 'en') {
        return ctx.redirect(`/${defaultLanguage}/`)
      }

      // Default
      ctx.req.language = defaultLanguage

      return await next()
    }
  }

  static getName(): string {
    return 'Redirect'
  }
}
