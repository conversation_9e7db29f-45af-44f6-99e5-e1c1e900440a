import url from 'url'
import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'
import { getQueryKey } from '../share/utils'

function getHostAccordingToUrl(uri: string): string {
  // eslint-disable-next-line node/no-deprecated-api
  if (url.parse) {
    // eslint-disable-next-line node/no-deprecated-api
    return url.parse(uri).host || ''
  }
  try {
    return new url.URL(uri).host || ''
  } catch (e) {}
  return ''
}

@Middleware()
export class WebsiteMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      if (ctx.isStaticOrApi) {
        await next()
        return
      }
      ctx.req.websiteConfig = ctx.websiteConfig

      // 方便测试环境模拟website
      if (process.env.APP_ENV !== 'production') {
        let QueryWebsite = getQueryKey('website', ctx.query)
        QueryWebsite = getHostAccordingToUrl(QueryWebsite) || ''

        QueryWebsite && ctx.cookies.set('website', QueryWebsite, {
          path: '/',
          expires: new Date(Date.now() + 30 * 24 * 3600 * 1000)
        })
      }

      return await next()
    }
  }

  static getName(): string {
    return 'website'
  }
}
