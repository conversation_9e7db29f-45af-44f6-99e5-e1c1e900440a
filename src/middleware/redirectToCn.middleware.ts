import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'
import { siteConfig } from '@klook/site-config'
import cookies from '../share/data/cookies'
import { getRegLangStr } from '../share/data/language'

enum PreferSite {
  com = '1',
  cn = '2'
}

const qs = (query: Record<string, string | number>) => {
  return Object.entries(query).map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`).join('&')
}

@Middleware()
export class RedirectToCnMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      /* eslint prefer-regex-literals: "warn" */
      // const HomeReg = new RegExp(
      //   '^/(|zh-CN/|zh-TW/|ko/|th/|vi/|zh-HK/|id/|ja/|en-NZ/|en-AU/|en-GB/|en-IN/|en-SG/|en-CA/|en-PH/|en-MY/|en-HK/|en-US/|de/|es/|ru/|fr/|it/)$'
      // )
      const HomeReg = new RegExp(getRegLangStr() + '$')

      const match = HomeReg.test(ctx.path)
      const isCNIp =
        ctx.country === 'CN' && (ctx.get('accept-language') || '').includes('zh-CN')

      // 非首页return
      if (!match) {
        await next()
        return
      }

      if (ctx.websiteConfig.market === 'global' && !ctx.deviceInfo.isKlookApp) {
        let klkPreferSiteCookie = ctx.cookies.get(cookies.preferSite.key, ctx.app.config.cookies) || ''

        if (!klkPreferSiteCookie) {
          // Detect siteCookie ipCountry === 'CN' && accept-language includes zh-CN => cn else com
          klkPreferSiteCookie = isCNIp ? PreferSite.cn : PreferSite.com
          ctx.cookies.set(cookies.preferSite.key, klkPreferSiteCookie, {
            path: '/',
            httpOnly: true,
            signed: false,
            expires: new Date(Date.now() + cookies.preferSite.lifetime)
          })
        }

        if (klkPreferSiteCookie && klkPreferSiteCookie === PreferSite.cn && isCNIp) {
          const newQs = qs(ctx.query as Record<string, string>)
          ctx.redirect(`https://${siteConfig.generateOtherLinks(ctx.realHost).cn}/zh-CN/${newQs ? '?' : ''}${newQs}`)
          return
        }
      }
      return await next()
    }
  }
}
