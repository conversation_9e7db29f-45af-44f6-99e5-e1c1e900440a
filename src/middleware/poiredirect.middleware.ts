import { IMiddleware } from '@midwayjs/core'
import { Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/web'

@Middleware()
export class PoiMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next) => {
      if (ctx.req.isStaticOrApi) {
        await next()
        return
      }

      const redirect = (rPath: string, isFullUrl: boolean = false) => {
        ctx.status = 301
        return ctx.redirect(isFullUrl ? rPath : ctx.path.replace(regPoi, rPath))
      }

      /* 匹配poi页
      * match: /poi/detail/20835409/ 或 /poi/20835409
      * */
      const regPoi = /\/poi\/detail\/(\d+)([^/]+)?\/?$/
      const matches = ctx.path.match(regPoi)

      if (matches && matches[1]) {
        if (isNaN(+matches[1])) {
          return redirect('/404/')
        }
        const querystring = ctx.querystring ? `?${ctx.querystring}` : ''
        ctx.status = 301

        if (!ctx.path.endsWith('/')) {
          return redirect(`${ctx.path.replace('/detail', '')}/${querystring}`, true)
        } else {
          return redirect(`${ctx.path.replace('/detail', '')}${querystring}`, true)
        }
      }

      return await next()
    }
  }
}
