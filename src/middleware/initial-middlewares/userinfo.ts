import { Context } from '@midwayjs/web'
import apis from '../../share/data/apis'
import cookies from '../../share/data/cookies'

const whiteList = ['/404', '/500', '/apiHealthCheck']

function checkWhiteList(url: string) {
  return whiteList.every(item => !url.includes(item))
}

const cookieOpt = {
  signed: false
}

export default async (ctx: Context) => {
  // 跳过静态资源和爬虫
  // 标识未登录
  if (ctx.isStaticOrApi || ctx.isSearchBot) {
    ctx.req.userInfo = null
    return
  }

  // 需要登录态的页面才调用接口
  if (checkWhiteList(ctx.url)) {
    const pt = ctx.cookies.get(cookies.token.key, cookieOpt) || ctx.cookies.get('_pt_gc', cookieOpt)

    // 没有pt 直接未登录
    if (!pt) {
      ctx.req.userInfo = null
      return
    }

    // 获取登录状态
    ctx.req.userInfo = await ctx.guestAxios
      .get(apis.verify)
      .then(({ data }: any) => {
        if (!data.success) {
          // 登录态失效
          return null
        }
        return data.result
      })
      .catch(() => {
        return null
      })
  }
}
