// import { Context } from 'egg'
import { Context } from '@midwayjs/web'

import apis from '../../share/data/apis'
import cookies from '../../share/data/cookies'
import codeMapping from './mapping'

function resolveResidence(residence: Data.Country) {
  const residenceId = codeMapping[residence]
  if (residenceId) {
    return {
      residenceCode: residence,
      residenceId
    }
  }

  // 默认 HK
  const defaultResidence: Data.Country = 'HK'
  return {
    residenceCode: defaultResidence,
    residenceId: codeMapping[defaultResidence]
  }
}

export default async function (ctx: Context) {
  const isBot = ctx.isSearchBot

  function setResidence(code: Data.Country, cacheHour = 0) {
    const { residenceCode, residenceId } = resolveResidence(code)
    ctx.req.residenceCode = residenceCode
    ctx.req.residenceId = residenceId

    // 是否需要设置cookie cache
    if (cacheHour > 0) {
      ctx.set_cookies(cookies.residence.key, residenceCode, {
        path: '/',
        signed: false,
        expires: new Date(Date.now() + cacheHour * 3600 * 1000),
        httpOnly: true
      })
    }
  }

  // bot
  if (ctx.isStaticOrApi || isBot) {
    setResidence(ctx.req.country)
    return
  }

  // 使用 cookie，检测合法值 直接使用，否则重新获取
  const cookieResidence = ctx.get_cookies(cookies.residence.key) as Data.Country
  if (cookieResidence && codeMapping[cookieResidence]) {
    setResidence(cookieResidence)
    return
  }

  // 通过接口获取
  const resData = await ctx.guestAxios.get(apis.assumedResidencev2, {
    timeout: 200
  })
    .then(({ data }: any) => {
      if (!(data?.success)) {
        throw new Error(`success false, ${data?.error?.message}`)
      }

      const result = data.result

      return {
        code: result.country_code,
        cacheHour: 24 // 成功记录 24小时
      }
    })
    .catch((error: any) => {
      const logquery = ctx.req.logquery
      const errMessage = (error.response || error.config) ? JSON.stringify({
        ...(error.response ? {
          status: error.response?.status,
          headers: error.response?.headers
        } : {
          response: false
        }),
        request: {
          url: error.config?.url,
          headers: error.request?.getHeaders?.()
        },
        requestId: ctx.requestId || ctx.req.requestId
      }) : error.message

      logquery?.service({
        timestamp: Date.now(),
        level: 'W',
        isMasked: true,
        funcName: 'assumedResidence.print',
        message: errMessage
      })

      return {
        code: ctx.country || '', // 降级到 country code
        cacheHour: Math.ceil(Math.random() * 3) // 削峰，随机 1-3 小时
      }
    })

  // set cookie httponly
  const { code, cacheHour } = resData
  setResidence(code, cacheHour)
}
