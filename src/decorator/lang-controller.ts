import { Controller, MiddlewareParamArray } from '@midwayjs/decorator'
import { languageConfig } from '@klook/site-config'

const langPrefix = `/:lang(|${languageConfig.supportLanguageList.join('|')})?`

export interface ControllerOption {
  routerOptions: {
    sensitive?: boolean;
    middleware?: MiddlewareParamArray;
    alias?: string[];
    description?: string;
    tagName?: string;
  };
}

export function LangController(
  routerOptions: {
    sensitive?: boolean;
    middleware?: MiddlewareParamArray;
    description?: string;
    tagName?: string;
  } = { middleware: [], sensitive: true }
): ClassDecorator {
  return Controller(langPrefix, routerOptions)
}
