// This file is created by egg-ts-helper
// Do not modify this file!!!!!!!!!
import 'egg';
import '@midwayjs/web';
import '@klook/klook-node-framework';
import 'egg-onerror';
import 'egg-session';
import 'egg-i18n';
import 'egg-watcher';
import 'egg-multipart';
import 'egg-security';
import 'egg-development';
import 'egg-logrotator';
import 'egg-schedule';
import 'egg-static';
import 'egg-jsonp';
import 'egg-view';
import '@klook/klook-node-framework-language';
import '@klook/klook-node-framework-country';
import '@klook/klook-node-framework-currency';
import '@klook/klook-node-framework-device';
import '@klook/klook-node-framework-logger';
import '@klook/klook-node-framework-site';
import '@klook/klook-node-framework-affiliate';
import '@klook/klook-node-framework-experiment';
import '@klook/klook-node-framework-axios';
import '@klook/klook-node-framework-i18n';
import '@klook/klook-node-framework-cache';
import '@klook/klook-node-framework-static-page';
import 'egg-redis';
import 'egg-lru';
import 'egg-alinode';
import '@klook/klook-node-framework-optimise';
import '@klook/egg-guardian';
import '@klook/klook-node-framework-ssr-render';
import { EggPluginItem } from 'egg';
declare module 'egg' {
  interface EggPlugin {
    'onerror'?: EggPluginItem;
    'session'?: EggPluginItem;
    'i18n'?: EggPluginItem;
    'watcher'?: EggPluginItem;
    'multipart'?: EggPluginItem;
    'security'?: EggPluginItem;
    'development'?: EggPluginItem;
    'logrotator'?: EggPluginItem;
    'schedule'?: EggPluginItem;
    'static'?: EggPluginItem;
    'jsonp'?: EggPluginItem;
    'view'?: EggPluginItem;
    'language'?: EggPluginItem;
    'country'?: EggPluginItem;
    'currency'?: EggPluginItem;
    'device'?: EggPluginItem;
    'logquery'?: EggPluginItem;
    'site'?: EggPluginItem;
    'affiliate'?: EggPluginItem;
    'experiment'?: EggPluginItem;
    'axios'?: EggPluginItem;
    'klkI18n'?: EggPluginItem;
    'cache'?: EggPluginItem;
    'staticPage'?: EggPluginItem;
    'redis'?: EggPluginItem;
    'lru'?: EggPluginItem;
    'alinode'?: EggPluginItem;
    'optimise'?: EggPluginItem;
    'guardian'?: EggPluginItem;
    'renderer'?: EggPluginItem;
  }
}