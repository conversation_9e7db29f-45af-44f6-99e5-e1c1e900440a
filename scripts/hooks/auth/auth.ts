import { resolve, join } from 'path'
import type { Klook } from '@klook/kiwi'

const generateAuth = (ctx) => {
  const app: Klook['app'] = ctx.app
  const klook: Klook = ctx.klook

  app.addTemplate({
    src: resolve(__dirname, './templates/middleware.tpl'),
    filename: join('auth', 'middleware.js')
  })

  app.addPlugin('../auth/middleware')
  const middlewares = klook.options.router.middleware || []

  middlewares.push('auth')

  klook.options.router.middleware = middlewares
}

const auth = (klook: Klook) => {
  klook.on('app:ready', async (ctx) => {
    await generateAuth(ctx)
  })
}

export default auth
