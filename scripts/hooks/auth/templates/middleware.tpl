import middleware from '../middleware'

// @NOTE: The route.meta field is an Array
// Ref: https://github.com/nuxt/nuxt.js/issues/5885#issuecomment-507670640
const detectAuthByRoute = function (key, value, route) {
  const meta = Array.isArray(route.meta) ? route.meta : [ route.meta ]
  return meta && meta.some(meta => meta[key] === value)
}

const detectIsServer = typeof process !== 'undefined' && typeof window === 'undefined' && Object.prototype.toString.call(process) === '[object process]';

/**
 * An auth middleware
 *
 * @example
 * // In component
 * export default {
 *  meta: {
 *    auth: true // true, false, undefined
 *  }
 * }
 *
 * // Or in route config
 * const routes = [{
 *   name: 'Home',
 *   path: '/',
 *   component: Home,
 *   meta: {
 *     auth: true // true, false, undefined
 *   }
 * }]
 */
middleware.auth = async ({ app, route, store, redirect, isServer, req }) => {
  // Public
  if (detectAuthByRoute('auth', false, route)) {
    return
  }

  let user

  // ues req.userInfo
  if(detectIsServer && req.userInfo !== undefined) {
    user = req.userInfo
  } else {
    // Check with time lock
    const canVerify = await store.dispatch('auth/canVerify')
    if (!canVerify) {
      return
    }

    // Default: verify token
    user = await store.dispatch('auth/verify')
  }

  app.user = user

  // Private
  if (detectAuthByRoute('auth', true, route)) {
    if (!user) {
      const callbackPath = encodeURIComponent(route.fullPath)
      redirect(`${app.$href('/signin')}/?signin_jump=${callbackPath}`)
    }
  }
}

export default () => {}
