import { resolve, join } from 'path'
import { Module } from '@klook/schema'

const main: Module = function () {
  // Add middleware by addPlugin
  this.addPlugin({
    src: resolve(__dirname, './templates/middleware.tpl'),
    fileName: join('auth', 'middleware.js')
  })

  // Apply middleware
  this.options.router = this.options.router || { middleware: [] }
  const { middleware } = this.options.router
  if (!middleware) {
    this.options.router.middleware = ['auth']
  } else if (typeof middleware === 'string') {
    this.options.router.middleware = [middleware, 'auth']
  } else if (Array.isArray(middleware)) {
    (this.options.router.middleware as string[]).push('auth')
  }
}

export default main
