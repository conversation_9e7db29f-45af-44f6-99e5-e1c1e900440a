import path from 'path'
import serialize from 'serialize-javascript'
import { serializeFunction } from '@nuxt/utils'
import { RouteItem } from './types'

interface Component {
  name: string
  chunkName: string
  path: string
}

interface Options {
  rootDir: string
  srcDir: string
  dir: string
}

/**
 * Generate a unique name for render router.js
 */
function genUniqueName(componentPath: string) {
  const chunkName = componentPath
    .replace(/\.(vue|js|ts)/g, '')
    .replace(/~+\//, '')
    .replace(/@+\//, '')
    .replace(/\//g, '-')
  const varName = chunkName
    .replace(/(-\w)/gi, $1 => $1.toUpperCase().replace('-', ''))

  return {
    varName,
    chunkName
  }
}

/**
 * Normalize routes
 */
export const normalizeRoutes = function (routes: RouteItem[], options: Options) {
  const components: Component[] = []

  function genRoutes(routes: RouteItem[], tab: string, tabCount: number): string[] {
    return routes.map((route) => {
      const pathAliasReg = /^[~@]/

      // Route terms
      const firstTab = tab.repeat(tabCount)
      const secondTab = tab.repeat(tabCount + 1)
      const thridTab = tab.repeat(tabCount + 2)
      const terms = [
        `${firstTab}{`
      ]

      if (typeof route.path === 'string') {
        terms.push(`${secondTab}path: '${route.path.replace(/\\/g, '\\\\')}',`)
      }

      if (route.name) {
        terms.push(`${secondTab}name: '${route.name}',`)
      }

      // Resolve alias
      if (route.alias) {
        terms.push(`${secondTab}alias: ${serialize(route.alias).replace(/"/g, "'")},`)
      }

      // Resolve caseSensitive
      if (typeof route.caseSensitive !== 'undefined') {
        terms.push(`${secondTab}caseSensitive: ${serialize(route.caseSensitive)},`)
      }

      // Resolve component
      if (route.component) {
        route.component = pathAliasReg.test(route.component)
          ? route.component
          : path.join(options.dir, route.component)

        const uniqueName = genUniqueName(route.component)
        components.push({
          name: uniqueName.varName,
          chunkName: uniqueName.chunkName,
          path: route.component
        })

        terms.push(`${secondTab}component: ${uniqueName.varName},`)
      }

      // Resolve components
      if (route.components) {
        terms.push(`${secondTab}components: {`)

        Object.keys(route.components).forEach((key: string) => {
          const componentPath = route.components![key]
          route.components![key] = pathAliasReg.test(componentPath)
            ? componentPath
            : path.join(options.dir, componentPath)

          const uniqueName = genUniqueName(route.components![key])
          components.push({
            name: uniqueName.varName,
            chunkName: uniqueName.chunkName,
            path: route.components![key]
          })

          terms.push(`${thridTab}${key}: ${uniqueName.varName},`)
        })

        terms.push(`${secondTab}},`)
      }

      // Resolve children
      if (route.children) {
        terms.push(`${secondTab}children: [`)
        terms.push(...genRoutes(route.children, tab, tabCount + 2))
        terms.push(`${secondTab}],`)
      }

      // Resolve pathToRegexpOptions
      // Use strict default
      route.pathToRegexpOptions = {
        strict: true,
        ...(route.pathToRegexpOptions || {})
      }
      terms.push(`${secondTab}pathToRegexpOptions: ${serialize(route.pathToRegexpOptions)},`)

      // Resolve meta
      if (route.meta) {
        route.meta = { ...route.meta }
        terms.push(`${secondTab}meta: ${serialize(route.meta, { ignoreFunction: true })},`)
      }

      // Resolve props
      if (route.props) {
        if (typeof route.props === 'function') {
          terms.push(`${secondTab}props: ${serializeFunction(route.props)},`)
        } else {
          terms.push(`${secondTab}props: ${serialize(route.props, { ignoreFunction: true })},`)
        }
      }

      // Resolve redirect
      if (route.redirect) {
        if (typeof route.redirect === 'function') {
          terms.push(`${secondTab}redirect: ${serializeFunction(route.redirect)},`)
        } else {
          terms.push(`${secondTab}redirect: ${serialize(route.redirect, { ignoreFunction: true })},`)
        }
      }

      // Resolve beforeEnter
      if (typeof route.beforeEnter === 'function') {
        terms.push(`${secondTab}beforeEnter: ${serializeFunction(route.beforeEnter)},`)
      }

      // Perfect close
      terms.push(`${firstTab}},`)

      return terms.join('\n')
    })
  }

  const routeTerms: string[] = genRoutes(routes, '  ', 3)

  return {
    components,
    routeTerms
  }
}
