import path from 'path'
import { Modu<PERSON> } from '@klook/schema'
import { normalizeRoutes } from './utils'
import { Options } from './types'

const main: Module<Options> = function (moduleOptions) {
  const defaultOptions: Options = {
    dir: '~/pages/',
    mode: 'history',
    base: '/',
    linkActiveClass: 'nuxt-link-active',
    linkExactActiveClass: 'nuxt-link-exact-active',
    middleware: undefined,
    prefetchLinks: false,
    routes: [],
    fallback: false
  }
  const options: Options = Object.assign(
    defaultOptions,
    this.options.customRouter as Options | undefined,
    moduleOptions
  )

  const { routeTerms, components } = normalizeRoutes(options.routes, {
    rootDir: this.nuxt.options.rootDir as string,
    srcDir: this.nuxt.options.srcDir as string,
    dir: options.dir as string
  })

  // Override router's prefetchLinks
  this.options.router!.prefetchLinks = options.prefetchLinks

  // Resolve middleware
  // resolveMiddleware(this.options, options)

  // Add plugin to import router file path as the main template for routing
  this.addPlugin({
    src: path.resolve(__dirname, './templates/router.tpl'),
    fileName: 'router.js',
    options: {
      ...options,
      components,
      routeTerms
    }
  })
}

export default main
