import fs from 'fs'
import path from 'path'
import type { Klook } from '@klook/kiwi'
import _ from 'lodash'
import { normalizeRoutes } from './utils'

// const pathToReg = require('path-to-regexp')

const tpl = fs.readFileSync(path.resolve(__dirname, './router.tpl'), 'utf8')

const collectRoutes = (ctx) => {
  const app: Klook['app'] & Record<string, any> = ctx.app

  const { routes } = ctx.app.options.customRouter as any
  console.log('\n routes', routes)
  const routePath = routes.map(i => i.path)

  const filter = (i) => {
    if (!i || /(40|50|^\/$)/.test(i)) { return false }
    return true
  }
  const filterRoutes = routePath.filter(filter).map(i => i.replace(/\/$/, ''))

  // const pathReg = pathToReg.pathToRegexp(filterRoutes, [], { end: false })

  app.addTemplate({
    filename: 'routePath.js',
    getContent() {
      return `
        const routeList = ${JSON.stringify(routePath)}
        const routeReg = "${routePath.join('|')}"

        const regexpRoutesArray = ${JSON.stringify(filterRoutes)}
      `
    }
  })

  const needFilterRoutes = routes

  const routeInfo = normalizeRoutes(needFilterRoutes, {
    rootDir: '',
    // srcDir: this.nuxt.options.srcDir as string,
    srcDir: '',
    // dir: options.dir as string
    dir: '@/pages'
  })

  const components = _.unionBy(routeInfo.components, 'name')
  const routeTerms = routeInfo.routeTerms

  app.router.customRouter = './defineRouter.ts'

  app.addTemplate({
    filename: 'defineRouter.ts',
    options: {
      options: {
        mode: 'history',
        base: '/',
        fallback: false,
        components,
        routeTerms
      }
    },
    getContent() {
      return tpl
    }
  })
}

export default function genRouter(klook: Klook) {
  klook.on('generate:template:ready', async (ctx) => {
    await collectRoutes(ctx)
  })
}
