import Vue from 'vue'
import Router from 'vue-router'
// import scrollBehavior from './router.scrollBehavior.js'

Vue.use(Router)

const interopDefault = function (promise) {
  return promise.then(m => m.default || m)
}

<%-
options.components.map(({ name, path, chunkName }) => {
  return `const ${name} = () => interopDefault(import('${path}' /* webpackChunkName: "${chunkName}" */))`
}).join('\n')
%>

export async function createRouter(ssrContext) {
  let options = {
    mode: '<%= options.mode %>',
    base: '<%= options.base %>',
    fallback: <%= options.fallback %>,
    linkActiveClass: '<%= options.linkActiveClass %>',
    linkExactActiveClass: '<%= options.linkExactActiveClass %>',
    // scrollBehavior,

    routes: [
<%- options.routeTerms.join('\n') %>
    ],
    <% if (options.parseQuery) { %>parseQuery: <%= serializeFunction(options.parseQuery) %>,<% } %>
    <% if (options.stringifyQuery) { %>stringifyQuery: <%= serializeFunction(options.stringifyQuery) %>,<% } %>
  }

  try {
    const { createRouter: customCreateRouter } = require('@/router')
    options = await customCreateRouter(ssrContext, options)
  } catch (error) {
    // Ignore
    console.error('Call createRouter error', error)
  }

  return new Router(options)
}
