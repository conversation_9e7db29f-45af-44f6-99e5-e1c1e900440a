import { RouterOptions, RouteConfig } from 'vue-router'

export interface RouteItem extends Omit<RouteConfig, 'name' | 'component' | 'components' | 'children'> {
  path: string
  name?: string
  component?: string
  components?: {
    [name: string]: string
  }
  children?: RouteItem[],
  layout?: Record<string, boolean>
  meta?: any
}

export type Routes = RouteItem[]

export interface Options extends Omit<RouterOptions, 'routes'> {
  dir?: string
  middleware?: string | string[]
  prefetchLinks?: boolean
  routes: Routes
}
