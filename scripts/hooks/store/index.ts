import path from 'path'
import fs from 'fs'
import type { Klook } from '@klook/kiwi'
import globby from 'globby'

const tpl = fs.readFileSync(path.resolve(__dirname, './store.tpl'), 'utf8')

const findStore = async (_klook?: Klook) => {
  const fileList = await globby(['./**/*.{js,ts,mjs}'], {
    cwd: path.resolve(process.cwd(), 'web/store')
  })
  const storeTemplate: string[] = []
  const storeImport: string[] = []
  const storeList: any = []
  if (fileList.length) {
    fileList.map((file) => {
      const namespaces = file.replace(path.extname(file), '')
      const basename = path.basename(file, path.extname(file))
      const filePath = '@/store/' + namespaces
      const fileName = '_' + namespaces.replace(/[\W]/ig, '')

      storeImport.push(`import * as ${fileName} from '${filePath}';`)

      return {
        namespaces,
        fileName,
        basename,
        filePath,
        file
      }
    }).forEach((item) => {
      storeList.push(`
      {
        namespaces: "${item.namespaces}",
        module: ${item.fileName},
      },
      `)
    })
  }
  storeTemplate.push(storeImport.join('\n'))
  storeTemplate.push(`
    const modules = [
      ${storeList.join('')}
    ];
  `)
  return storeTemplate.join('\n')
}

const replaceStore = async ({ app }) => {
  const storeTemplate = await findStore()

  app.store.customStore = '@build/defineStore.ts'

  app.addTemplate({
    filename: 'defineStore.ts',
    options: {
      storeTemplate
    },
    getContent() {
      return tpl
    }
  })
}

export default function genStore(klook: Klook) {
  klook.on('generate:template:ready', async (ctx) => {
    await replaceStore(ctx)
  })
}
