
import Vue from 'vue'
import Vuex from 'vuex'

<%- storeTemplate %>

Vue.use(Vuex)

const Store_Rules = ['state', 'getters', 'actions', 'mutations']

function deepModule(namespaces, state, storeModule) {
  const namespace = namespaces.shift()
  if (!namespaces.length) {

    if (namespace === 'index' || Store_Rules.includes(namespace)) {
      state = {
        ...state,
        ...storeModule
      }
    } else {
      state.modules[namespace] = state.modules[namespace] || {
        namespaced: true,
        ...storeModule
      }
    }
    return state
  } else {
    if (state.modules) {
      state.modules[namespace] = state.modules[namespace] || { namespaced: true }
      state.modules[namespace].modules = state.modules[namespace].modules || {}
      state.modules[namespace] = deepModule(namespaces, state.modules[namespace], storeModule)
    } else {
      state[namespace] = state[namespace] || { namespaced: true }
      state[namespace].modules = state[namespace].modules || {}
      state[namespace] = deepModule(namespaces, state[namespace], storeModule)
    }
  }
  return state
}

function normalizeModule(state: Record<string, any>) {
  if (modules.length) {
    modules.forEach(module => {
      if (module.namespaces === 'index') {
        Object.assign(state, module.module)
      } else {
        const namespaces = module.namespaces.split('/')
        const storeModules = state.modules = state.modules || {}
        deepModule(namespaces, state, module.module)
      }
    })
  }
  return state
}

export function createStore<T = unknown>() {
  const state = {}
  normalizeModule(state)

  const modules = state.modules || {}
  delete state.modules

  const store = new Vuex.Store<typeof state>({
    strict: true,
    ...state,
    modules
  })

  return store
}


