import type { Klook } from '@klook/kiwi'

const svgHook = (klook: Klook) => {
  const svg = require('@klook/nuxt-svg-sprite')
  const img = require('@klook/nuxt-image-sprite')

  const options = klook.options

  const { nuxtImageSprite, nuxtSvgSprite } = options as any

  const nuxt = {
    options: {
      dev: options.dev,
      srcDir: options.webDir,
      rootDir: options.rootDir
    },
    hook(event, cb) {
      if (event) {
        klook.on(event, cb)
      }
    }
  }

  svg.call({
    options: {
      nuxtSvgSprite
    },
    nuxt
  }, {})

  img.call({
    options: {
      nuxtImageSprite
    },
    nuxt
  }, {})
}

export default svgHook
