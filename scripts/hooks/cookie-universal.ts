
import type { Klook } from '@klook/kiwi'

const cookieUniversalPlugin = ({ app }) => {
  app.addTemplate({
    filename: 'cookie-universe.ts',
    getContent() {
      return `
import cookieUniversal from 'cookie-universal'
export default ({ req, res }, inject) => {
const options = {
  alias: 'cookies',
  parseJSON: JSON.parse,
}
inject(options.alias, cookieUniversal(req, res, options.parseJSON))
}
    `
    }
  })

  app.addPlugin('../cookie-universe', { first: true })
}

const cookieUniverse = (klook: Klook) => {
  klook.on('app:ready', async (ctx) => {
    await cookieUniversalPlugin(ctx)
  })
}

export default cookieUniverse
