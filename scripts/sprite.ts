
export const nuxtImageSprite = {
  sprites: [
    {
      name: 'desktop-common',
      input: '~/assets/desktop/sprites/img-common',
      output: '~/assets/sprites/output',
      image: '~assets/sprites/output/desktop-common.png'
    }
  ],
  retina: true,
  format: 'scss'
}

export const nuxtSvgSprite = {
  sprites: [
    {
      name: 'common',
      input: '~/assets/sprites/svg-common',
      output: '~/assets/sprites/output'
    },
    {
      name: 'desktop-common',
      input: '~/assets/desktop/sprites/svg-common',
      output: '~/assets/sprites/output'
    },
    {
      name: 'd-homepage',
      input: '~/assets/desktop/sprites/svg-homepage',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-common',
      input: '~/assets/mobile/sprites/svg-common',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-hotel',
      input: '~/assets/mobile/sprites/svg-hotel',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-car-rental',
      input: '~/assets/mobile/sprites/svg-car-rental',
      output: '~/assets/sprites/output'
    },
    {
      name: 'activity-common',
      input: '~/assets/sprites/svg-activity',
      output: '~/assets/sprites/output'
    },
    {
      name: 'desktop-ptp',
      input: '~/assets/desktop/sprites/svg-ptp',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-ptp',
      input: '~/assets/mobile/sprites/svg-ptp',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-activity',
      input: '~/assets/mobile/sprites/svg-activity',
      output: '~/assets/sprites/output'
    },
    {
      name: 'desktop-activity',
      input: '~/assets/desktop/sprites/svg-activity',
      output: '~/assets/sprites/output'
    },
    {
      name: 'desktop-bus',
      input: '~/assets/desktop/sprites/svg-bus',
      output: '~/assets/sprites/output'
    }, {
      name: 'desktop-car-rental',
      input: '~/assets/desktop/sprites/svg-car-rental',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-home',
      input: '~/assets/mobile/sprites/svg-home',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-event',
      input: '~/assets/mobile/sprites/svg-event',
      output: '~/assets/sprites/output'
    },
    {
      name: 'desktop-event',
      input: '~/assets/desktop/sprites/svg-event',
      output: '~/assets/sprites/output'
    },
    {
      name: 'mobile-partnership',
      input: '~/assets/mobile/sprites/svg-partnership',
      output: '~/assets/sprites/output'
    }, {
      name: 'contact-tracking',
      input: '~/assets/mobile/sprites/svg-contact-tracking',
      output: '~/assets/sprites/output'
    },
    // @NOTE: 增加实验 svg icon 存放目录, 便于删除, icon="experiments#icon_name"
    {
      name: 'experiments',
      input: '~/experiments/svg',
      output: '~/assets/sprites/output'
    },
    {
      name: 'desktop-experience',
      input: '~/assets/desktop/sprites/svg-experience',
      output: '~/assets/sprites/output'
    },
    {
      name: 'fnb-common',
      input: '~/assets/sprites/svg-fnb',
      output: '~/assets/sprites/output'
    }
  ],
  scoped: true
}
