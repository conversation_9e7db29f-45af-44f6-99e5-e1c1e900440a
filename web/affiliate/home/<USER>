
import { Vue, Component, State } from 'nuxt-property-decorator'
import { getAffiliateItemConf } from '~/share/utils'
import { getSessionData, setSessionData } from '~/assets/scripts/sessionstorage'

@Component({})
export default class HotelListVue extends Vue {
  @State klook!: Data.Klook

  klook_service_pop = false

  mounted() {
    this.init_klook_service_pop()
  }

  init_klook_service_pop() {
    if (
      getAffiliateItemConf(this, 'partner_web_config.main_page_popup', false) &&
      !getSessionData('main_page_popup')
    ) {
      this.klook_service_pop = true
      setSessionData('main_page_popup', true)
      setTimeout(() => {
        this.klook_service_pop = false
      }, 5000)
    }
  }

  closeNotification() {
    this.klook_service_pop = false
  }
}
