<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="24" viewBox="0 0 750 24">
    <defs>
        <path id="prefix__a" d="M0 0H750V24H0z"/>
        <path id="prefix__d" d="M750-336V2.73L75 2.729c-6.446.114-12.125 1.816-17.035 5.107l-7.001 6.995c-1.565 1.559-4.095 1.559-5.66 0l-6.98-6.975c-4.799-3.229-10.331-4.938-16.599-5.127L0 2.73V-336h750z"/>
        <filter id="prefix__c" width="100.1%" height="100.3%" x="0%" y="-.1%" filterUnits="objectBoundingBox">
            <feOffset dy=".5" in="SourceAlpha" result="shadowOffsetOuter1"/>
            <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0.878431373 0 0 0 0 0.878431373 0 0 0 0 0.878431373 0 0 0 1 0"/>
        </filter>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <mask id="prefix__b" fill="#fff">
            <use xlink:href="#prefix__a"/>
        </mask>
        <g mask="url(#prefix__b)">
            <use fill="#000" filter="url(#prefix__c)" xlink:href="#prefix__d"/>
            <use fill="#FFF" xlink:href="#prefix__d"/>
        </g>
    </g>
</svg>
