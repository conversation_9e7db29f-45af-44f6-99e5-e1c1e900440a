<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="38" height="38" viewBox="0 0 38 38">
    <defs>
        <filter id="prefix__b" width="105.5%" height="105.5%" x="-2.8%" y="-2.8%" filterUnits="objectBoundingBox">
            <feGaussianBlur in="SourceAlpha" result="shadowBlurInner1" stdDeviation="1"/>
            <feOffset in="shadowBlurInner1" result="shadowOffsetInner1"/>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" k2="-1" k3="1" operator="arithmetic" result="shadowInnerInner1"/>
            <feColorMatrix in="shadowInnerInner1" values="0 0 0 0 0.941176471 0 0 0 0 0.678431373 0 0 0 0 0.317647059 0 0 0 1 0"/>
        </filter>
        <filter id="prefix__d" width="131.6%" height="131.6%" x="-15.8%" y="-15.8%" filterUnits="objectBoundingBox">
            <feGaussianBlur in="SourceGraphic" stdDeviation="2"/>
        </filter>
        <filter id="prefix__f" width="299.4%" height="299.4%" x="-99.7%" y="-99.7%" filterUnits="objectBoundingBox">
            <feGaussianBlur in="SourceGraphic" stdDeviation="6"/>
        </filter>
        <filter id="prefix__g" width="100%" height="100%" x="0%" y="0%" filterUnits="objectBoundingBox">
            <feGaussianBlur in="SourceGraphic"/>
        </filter>
        <filter id="prefix__h" width="100%" height="100%" x="0%" y="0%" filterUnits="objectBoundingBox">
            <feGaussianBlur in="SourceGraphic"/>
        </filter>
        <linearGradient id="prefix__c" x1="84.664%" x2="20.037%" y1="5.612%" y2="81.745%">
            <stop offset="0%" stop-color="#FFF1BB"/>
            <stop offset="100%" stop-color="#FFCE42"/>
        </linearGradient>
        <circle id="prefix__a" cx="18.05" cy="18.05" r="18.05"/>
        <path id="prefix__i" d="M4.668 0L.475.004C.213.004 0 .217 0 .48c0 4.722 3.828 8.55 8.55 8.55 4.722 0 8.55-3.828 8.55-8.55 0-.262-.213-.475-.475-.475l-5.909.004L4.668 0z"/>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <g transform="translate(.95 .95)">
            <mask id="prefix__e" fill="#fff">
                <use xlink:href="#prefix__a"/>
            </mask>
            <g fill-rule="nonzero">
                <use fill="#F6AB32" xlink:href="#prefix__a"/>
                <use fill="#000" filter="url(#prefix__b)" xlink:href="#prefix__a"/>
            </g>
            <circle cx="23.75" cy="13.3" r="19" fill="url(#prefix__c)" fill-rule="nonzero" filter="url(#prefix__d)" mask="url(#prefix__e)"/>
            <circle cx="26.125" cy="7.125" r="9.025" fill="#FFF5B9" fill-rule="nonzero" filter="url(#prefix__f)" mask="url(#prefix__e)"/>
        </g>
        <ellipse cx="7.125" cy="21.375" fill="#FE834A" filter="url(#prefix__g)" opacity=".272" rx="4.275" ry="3.325"/>
        <ellipse cx="30.875" cy="21.375" fill="#FE834A" filter="url(#prefix__h)" opacity=".272" rx="4.275" ry="3.325"/>
        <g transform="translate(9.975 20.9)">
            <mask id="prefix__j" fill="#fff">
                <use xlink:href="#prefix__i"/>
            </mask>
            <use fill="#B15D45" fill-rule="nonzero" xlink:href="#prefix__i"/>
            <ellipse cx="11.4" cy="8.075" fill="#FF7A3D" mask="url(#prefix__j)" rx="9.5" ry="4.75"/>
        </g>
        <g fill="#424242" transform="translate(10.45 13.3)">
            <circle cx="1.9" cy="1.9" r="1.9"/>
            <circle cx="15.2" cy="1.9" r="1.9"/>
        </g>
    </g>
</svg>
