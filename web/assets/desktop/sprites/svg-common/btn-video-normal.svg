<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100" height="100" viewBox="0 0 100 100">
    <defs>
        <path id="b" d="M50 18c17.673 0 32 14.327 32 32 0 17.673-14.327 32-32 32-17.673 0-32-14.327-32-32 0-17.673 14.327-32 32-32zm-5.195 23.097v19.05c0 1.082.859 1.786 1.59 1.303l14.372-9.51c.81-.536.811-2.069.002-2.606l-14.373-9.54c-.731-.485-1.59.22-1.59 1.303z"/>
        <filter id="a" width="172.7%" height="172.7%" x="-36.3%" y="-23.8%" filterUnits="objectBoundingBox">
            <feMorphology in="SourceAlpha" operator="dilate" radius="1.25" result="shadowSpreadOuter1"/>
            <feOffset dy="8" in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
            <feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="6"/>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"/>
            <feColorMatrix in="shadowBlurOuter1" result="shadowMatrixOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
            <feMorphology in="SourceAlpha" operator="dilate" radius="1.25" result="shadowSpreadOuter2"/>
            <feOffset dy="-1" in="shadowSpreadOuter2" result="shadowOffsetOuter2"/>
            <feGaussianBlur in="shadowOffsetOuter2" result="shadowBlurOuter2" stdDeviation="1"/>
            <feComposite in="shadowBlurOuter2" in2="SourceAlpha" operator="out" result="shadowBlurOuter2"/>
            <feColorMatrix in="shadowBlurOuter2" result="shadowMatrixOuter2" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"/>
                <feMergeNode in="shadowMatrixOuter2"/>
            </feMerge>
        </filter>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <path d="M0 0h100v100H0z"/>
        <g>
            <use fill="#000" filter="url(#a)" xlink:href="#b"/>
            <use fill="#D8D8D8" fill-opacity=".5" stroke="#FFF" stroke-width="2.5" xlink:href="#b"/>
        </g>
    </g>
</svg>
