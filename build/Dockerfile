# klook-nuxt-web
FROM registry.klook.io/node/node:16-slim
<PERSON>BEL maintainer="klook front-end team"

# Setting time zone
# RUN apk update && apk add ca-certificates && \
#     apk add tzdata && \
#     ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
#     echo "Asia/Shanghai" > /etc/timezone

WORKDIR /app

COPY package.json package-lock.json .npmrc /app/
RUN npm install --production && npm cache clean --force
COPY . /app/

# if target file exist, COPY command will override it. It meets our expectation.
# COPY ./dist /app/
# COPY app-config.json $HOME

ENV EGG_WORKERS=2
ENV NODE_ENV=production
ENV TZ=Asia/Hong_Kong
ENV KLOOK_TARGET_ENV=aws

EXPOSE 8080

CMD ["npm", "run", "start:prod"]
